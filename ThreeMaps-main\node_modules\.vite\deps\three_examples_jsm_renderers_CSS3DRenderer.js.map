{"version": 3, "sources": ["../../three/examples/jsm/renderers/CSS3DRenderer.js"], "sourcesContent": ["import {\n\tMatrix4,\n\tObject3D,\n\tQuaternion,\n\tVector3\n} from 'three';\n\n/**\n * Based on http://www.emagix.net/academic/mscs-project/item/camera-sync-with-css3-and-webgl-threejs\n */\n\nconst _position = new Vector3();\nconst _quaternion = new Quaternion();\nconst _scale = new Vector3();\n\nclass CSS3DObject extends Object3D {\n\n\tconstructor( element = document.createElement( 'div' ) ) {\n\n\t\tsuper();\n\n\t\tthis.isCSS3DObject = true;\n\n\t\tthis.element = element;\n\t\tthis.element.style.position = 'absolute';\n\t\tthis.element.style.pointerEvents = 'auto';\n\t\tthis.element.style.userSelect = 'none';\n\n\t\tthis.element.setAttribute( 'draggable', false );\n\n\t\tthis.addEventListener( 'removed', function () {\n\n\t\t\tthis.traverse( function ( object ) {\n\n\t\t\t\tif ( object.element instanceof Element && object.element.parentNode !== null ) {\n\n\t\t\t\t\tobject.element.parentNode.removeChild( object.element );\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n\tcopy( source, recursive ) {\n\n\t\tsuper.copy( source, recursive );\n\n\t\tthis.element = source.element.cloneNode( true );\n\n\t\treturn this;\n\n\t}\n\n}\n\nclass CSS3DSprite extends CSS3DObject {\n\n\tconstructor( element ) {\n\n\t\tsuper( element );\n\n\t\tthis.isCSS3DSprite = true;\n\n\t\tthis.rotation2D = 0;\n\n\t}\n\n\tcopy( source, recursive ) {\n\n\t\tsuper.copy( source, recursive );\n\n\t\tthis.rotation2D = source.rotation2D;\n\n\t\treturn this;\n\n\t}\n\n}\n\n//\n\nconst _matrix = new Matrix4();\nconst _matrix2 = new Matrix4();\n\nclass CSS3DRenderer {\n\n\tconstructor( parameters = {} ) {\n\n\t\tconst _this = this;\n\n\t\tlet _width, _height;\n\t\tlet _widthHalf, _heightHalf;\n\n\t\tconst cache = {\n\t\t\tcamera: { style: '' },\n\t\t\tobjects: new WeakMap()\n\t\t};\n\n\t\tconst domElement = parameters.element !== undefined ? parameters.element : document.createElement( 'div' );\n\n\t\tdomElement.style.overflow = 'hidden';\n\n\t\tthis.domElement = domElement;\n\n\t\tconst viewElement = document.createElement( 'div' );\n\t\tviewElement.style.transformOrigin = '0 0';\n\t\tviewElement.style.pointerEvents = 'none';\n\t\tdomElement.appendChild( viewElement );\n\n\t\tconst cameraElement = document.createElement( 'div' );\n\n\t\tcameraElement.style.transformStyle = 'preserve-3d';\n\n\t\tviewElement.appendChild( cameraElement );\n\n\t\tthis.getSize = function () {\n\n\t\t\treturn {\n\t\t\t\twidth: _width,\n\t\t\t\theight: _height\n\t\t\t};\n\n\t\t};\n\n\t\tthis.render = function ( scene, camera ) {\n\n\t\t\tconst fov = camera.projectionMatrix.elements[ 5 ] * _heightHalf;\n\n\t\t\tif ( camera.view && camera.view.enabled ) {\n\n\t\t\t\t// view offset\n\t\t\t\tviewElement.style.transform = `translate( ${ - camera.view.offsetX * ( _width / camera.view.width ) }px, ${ - camera.view.offsetY * ( _height / camera.view.height ) }px )`;\n\n\t\t\t\t// view fullWidth and fullHeight, view width and height\n\t\t\t\tviewElement.style.transform += `scale( ${ camera.view.fullWidth / camera.view.width }, ${ camera.view.fullHeight / camera.view.height } )`;\n\n\t\t\t} else {\n\n\t\t\t\tviewElement.style.transform = '';\n\n\t\t\t}\n\n\t\t\tif ( scene.matrixWorldAutoUpdate === true ) scene.updateMatrixWorld();\n\t\t\tif ( camera.parent === null && camera.matrixWorldAutoUpdate === true ) camera.updateMatrixWorld();\n\n\t\t\tlet tx, ty;\n\n\t\t\tif ( camera.isOrthographicCamera ) {\n\n\t\t\t\ttx = - ( camera.right + camera.left ) / 2;\n\t\t\t\tty = ( camera.top + camera.bottom ) / 2;\n\n\t\t\t}\n\n\t\t\tconst scaleByViewOffset = camera.view && camera.view.enabled ? camera.view.height / camera.view.fullHeight : 1;\n\t\t\tconst cameraCSSMatrix = camera.isOrthographicCamera ?\n\t\t\t\t`scale( ${ scaleByViewOffset } )` + 'scale(' + fov + ')' + 'translate(' + epsilon( tx ) + 'px,' + epsilon( ty ) + 'px)' + getCameraCSSMatrix( camera.matrixWorldInverse ) :\n\t\t\t\t`scale( ${ scaleByViewOffset } )` + 'translateZ(' + fov + 'px)' + getCameraCSSMatrix( camera.matrixWorldInverse );\n\t\t\tconst perspective = camera.isPerspectiveCamera ? 'perspective(' + fov + 'px) ' : '';\n\n\t\t\tconst style = perspective + cameraCSSMatrix +\n\t\t\t\t'translate(' + _widthHalf + 'px,' + _heightHalf + 'px)';\n\n\t\t\tif ( cache.camera.style !== style ) {\n\n\t\t\t\tcameraElement.style.transform = style;\n\n\t\t\t\tcache.camera.style = style;\n\n\t\t\t}\n\n\t\t\trenderObject( scene, scene, camera, cameraCSSMatrix );\n\n\t\t};\n\n\t\tthis.setSize = function ( width, height ) {\n\n\t\t\t_width = width;\n\t\t\t_height = height;\n\t\t\t_widthHalf = _width / 2;\n\t\t\t_heightHalf = _height / 2;\n\n\t\t\tdomElement.style.width = width + 'px';\n\t\t\tdomElement.style.height = height + 'px';\n\n\t\t\tviewElement.style.width = width + 'px';\n\t\t\tviewElement.style.height = height + 'px';\n\n\t\t\tcameraElement.style.width = width + 'px';\n\t\t\tcameraElement.style.height = height + 'px';\n\n\t\t};\n\n\t\tfunction epsilon( value ) {\n\n\t\t\treturn Math.abs( value ) < 1e-10 ? 0 : value;\n\n\t\t}\n\n\t\tfunction getCameraCSSMatrix( matrix ) {\n\n\t\t\tconst elements = matrix.elements;\n\n\t\t\treturn 'matrix3d(' +\n\t\t\t\tepsilon( elements[ 0 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 1 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 2 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 3 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 4 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 5 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 6 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 7 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 8 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 9 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 10 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 11 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 12 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 13 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 14 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 15 ] ) +\n\t\t\t')';\n\n\t\t}\n\n\t\tfunction getObjectCSSMatrix( matrix ) {\n\n\t\t\tconst elements = matrix.elements;\n\t\t\tconst matrix3d = 'matrix3d(' +\n\t\t\t\tepsilon( elements[ 0 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 1 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 2 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 3 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 4 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 5 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 6 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 7 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 8 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 9 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 10 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 11 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 12 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 13 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 14 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 15 ] ) +\n\t\t\t')';\n\n\t\t\treturn 'translate(-50%,-50%)' + matrix3d;\n\n\t\t}\n\n\t\tfunction renderObject( object, scene, camera, cameraCSSMatrix ) {\n\n\t\t\tif ( object.isCSS3DObject ) {\n\n\t\t\t\tconst visible = ( object.visible === true ) && ( object.layers.test( camera.layers ) === true );\n\t\t\t\tobject.element.style.display = ( visible === true ) ? '' : 'none';\n\n\t\t\t\tif ( visible === true ) {\n\n\t\t\t\t\tobject.onBeforeRender( _this, scene, camera );\n\n\t\t\t\t\tlet style;\n\n\t\t\t\t\tif ( object.isCSS3DSprite ) {\n\n\t\t\t\t\t\t// http://swiftcoder.wordpress.com/2008/11/25/constructing-a-billboard-matrix/\n\n\t\t\t\t\t\t_matrix.copy( camera.matrixWorldInverse );\n\t\t\t\t\t\t_matrix.transpose();\n\n\t\t\t\t\t\tif ( object.rotation2D !== 0 ) _matrix.multiply( _matrix2.makeRotationZ( object.rotation2D ) );\n\n\t\t\t\t\t\tobject.matrixWorld.decompose( _position, _quaternion, _scale );\n\t\t\t\t\t\t_matrix.setPosition( _position );\n\t\t\t\t\t\t_matrix.scale( _scale );\n\n\t\t\t\t\t\t_matrix.elements[ 3 ] = 0;\n\t\t\t\t\t\t_matrix.elements[ 7 ] = 0;\n\t\t\t\t\t\t_matrix.elements[ 11 ] = 0;\n\t\t\t\t\t\t_matrix.elements[ 15 ] = 1;\n\n\t\t\t\t\t\tstyle = getObjectCSSMatrix( _matrix );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tstyle = getObjectCSSMatrix( object.matrixWorld );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst element = object.element;\n\t\t\t\t\tconst cachedObject = cache.objects.get( object );\n\n\t\t\t\t\tif ( cachedObject === undefined || cachedObject.style !== style ) {\n\n\t\t\t\t\t\telement.style.transform = style;\n\n\t\t\t\t\t\tconst objectData = { style: style };\n\t\t\t\t\t\tcache.objects.set( object, objectData );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( element.parentNode !== cameraElement ) {\n\n\t\t\t\t\t\tcameraElement.appendChild( element );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tobject.onAfterRender( _this, scene, camera );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0, l = object.children.length; i < l; i ++ ) {\n\n\t\t\t\trenderObject( object.children[ i ], scene, camera, cameraCSSMatrix );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\nexport { CSS3DObject, CSS3DSprite, CSS3DRenderer };\n"], "mappings": ";;;;;;;;;AAWA,IAAM,YAAY,IAAI,QAAQ;AAC9B,IAAM,cAAc,IAAI,WAAW;AACnC,IAAM,SAAS,IAAI,QAAQ;AAE3B,IAAM,cAAN,cAA0B,SAAS;AAAA,EAElC,YAAa,UAAU,SAAS,cAAe,KAAM,GAAI;AAExD,UAAM;AAEN,SAAK,gBAAgB;AAErB,SAAK,UAAU;AACf,SAAK,QAAQ,MAAM,WAAW;AAC9B,SAAK,QAAQ,MAAM,gBAAgB;AACnC,SAAK,QAAQ,MAAM,aAAa;AAEhC,SAAK,QAAQ,aAAc,aAAa,KAAM;AAE9C,SAAK,iBAAkB,WAAW,WAAY;AAE7C,WAAK,SAAU,SAAW,QAAS;AAElC,YAAK,OAAO,mBAAmB,WAAW,OAAO,QAAQ,eAAe,MAAO;AAE9E,iBAAO,QAAQ,WAAW,YAAa,OAAO,OAAQ;AAAA,QAEvD;AAAA,MAED,CAAE;AAAA,IAEH,CAAE;AAAA,EAEH;AAAA,EAEA,KAAM,QAAQ,WAAY;AAEzB,UAAM,KAAM,QAAQ,SAAU;AAE9B,SAAK,UAAU,OAAO,QAAQ,UAAW,IAAK;AAE9C,WAAO;AAAA,EAER;AAED;AAEA,IAAM,cAAN,cAA0B,YAAY;AAAA,EAErC,YAAa,SAAU;AAEtB,UAAO,OAAQ;AAEf,SAAK,gBAAgB;AAErB,SAAK,aAAa;AAAA,EAEnB;AAAA,EAEA,KAAM,QAAQ,WAAY;AAEzB,UAAM,KAAM,QAAQ,SAAU;AAE9B,SAAK,aAAa,OAAO;AAEzB,WAAO;AAAA,EAER;AAED;AAIA,IAAM,UAAU,IAAI,QAAQ;AAC5B,IAAM,WAAW,IAAI,QAAQ;AAE7B,IAAM,gBAAN,MAAoB;AAAA,EAEnB,YAAa,aAAa,CAAC,GAAI;AAE9B,UAAM,QAAQ;AAEd,QAAI,QAAQ;AACZ,QAAI,YAAY;AAEhB,UAAM,QAAQ;AAAA,MACb,QAAQ,EAAE,OAAO,GAAG;AAAA,MACpB,SAAS,oBAAI,QAAQ;AAAA,IACtB;AAEA,UAAM,aAAa,WAAW,YAAY,SAAY,WAAW,UAAU,SAAS,cAAe,KAAM;AAEzG,eAAW,MAAM,WAAW;AAE5B,SAAK,aAAa;AAElB,UAAM,cAAc,SAAS,cAAe,KAAM;AAClD,gBAAY,MAAM,kBAAkB;AACpC,gBAAY,MAAM,gBAAgB;AAClC,eAAW,YAAa,WAAY;AAEpC,UAAM,gBAAgB,SAAS,cAAe,KAAM;AAEpD,kBAAc,MAAM,iBAAiB;AAErC,gBAAY,YAAa,aAAc;AAEvC,SAAK,UAAU,WAAY;AAE1B,aAAO;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,MACT;AAAA,IAED;AAEA,SAAK,SAAS,SAAW,OAAO,QAAS;AAExC,YAAM,MAAM,OAAO,iBAAiB,SAAU,CAAE,IAAI;AAEpD,UAAK,OAAO,QAAQ,OAAO,KAAK,SAAU;AAGzC,oBAAY,MAAM,YAAY,cAAe,CAAE,OAAO,KAAK,WAAY,SAAS,OAAO,KAAK,MAAQ,OAAQ,CAAE,OAAO,KAAK,WAAY,UAAU,OAAO,KAAK,OAAS;AAGrK,oBAAY,MAAM,aAAa,UAAW,OAAO,KAAK,YAAY,OAAO,KAAK,KAAM,KAAM,OAAO,KAAK,aAAa,OAAO,KAAK,MAAO;AAAA,MAEvI,OAAO;AAEN,oBAAY,MAAM,YAAY;AAAA,MAE/B;AAEA,UAAK,MAAM,0BAA0B;AAAO,cAAM,kBAAkB;AACpE,UAAK,OAAO,WAAW,QAAQ,OAAO,0BAA0B;AAAO,eAAO,kBAAkB;AAEhG,UAAI,IAAI;AAER,UAAK,OAAO,sBAAuB;AAElC,aAAK,EAAI,OAAO,QAAQ,OAAO,QAAS;AACxC,cAAO,OAAO,MAAM,OAAO,UAAW;AAAA,MAEvC;AAEA,YAAM,oBAAoB,OAAO,QAAQ,OAAO,KAAK,UAAU,OAAO,KAAK,SAAS,OAAO,KAAK,aAAa;AAC7G,YAAM,kBAAkB,OAAO,uBAC9B,UAAW,iBAAkB,aAAkB,MAAM,gBAAqB,QAAS,EAAG,IAAI,QAAQ,QAAS,EAAG,IAAI,QAAQ,mBAAoB,OAAO,kBAAmB,IACxK,UAAW,iBAAkB,kBAAuB,MAAM,QAAQ,mBAAoB,OAAO,kBAAmB;AACjH,YAAM,cAAc,OAAO,sBAAsB,iBAAiB,MAAM,SAAS;AAEjF,YAAM,QAAQ,cAAc,kBAC3B,eAAe,aAAa,QAAQ,cAAc;AAEnD,UAAK,MAAM,OAAO,UAAU,OAAQ;AAEnC,sBAAc,MAAM,YAAY;AAEhC,cAAM,OAAO,QAAQ;AAAA,MAEtB;AAEA,mBAAc,OAAO,OAAO,QAAQ,eAAgB;AAAA,IAErD;AAEA,SAAK,UAAU,SAAW,OAAO,QAAS;AAEzC,eAAS;AACT,gBAAU;AACV,mBAAa,SAAS;AACtB,oBAAc,UAAU;AAExB,iBAAW,MAAM,QAAQ,QAAQ;AACjC,iBAAW,MAAM,SAAS,SAAS;AAEnC,kBAAY,MAAM,QAAQ,QAAQ;AAClC,kBAAY,MAAM,SAAS,SAAS;AAEpC,oBAAc,MAAM,QAAQ,QAAQ;AACpC,oBAAc,MAAM,SAAS,SAAS;AAAA,IAEvC;AAEA,aAAS,QAAS,OAAQ;AAEzB,aAAO,KAAK,IAAK,KAAM,IAAI,QAAQ,IAAI;AAAA,IAExC;AAEA,aAAS,mBAAoB,QAAS;AAErC,YAAM,WAAW,OAAO;AAExB,aAAO,cACN,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,CAAE,SAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,CAAE,SAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,CAAE,SAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,SAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,SAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,SAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,CAAE,SAAU,EAAG,CAAE,IAAI,MAC9B,QAAS,SAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,SAAU,EAAG,CAAE,IACzB;AAAA,IAED;AAEA,aAAS,mBAAoB,QAAS;AAErC,YAAM,WAAW,OAAO;AACxB,YAAM,WAAW,cAChB,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,CAAE,SAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,CAAE,SAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,CAAE,SAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,CAAE,SAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,SAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,SAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,SAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,SAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,SAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,SAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,SAAU,EAAG,CAAE,IACzB;AAEA,aAAO,yBAAyB;AAAA,IAEjC;AAEA,aAAS,aAAc,QAAQ,OAAO,QAAQ,iBAAkB;AAE/D,UAAK,OAAO,eAAgB;AAE3B,cAAM,UAAY,OAAO,YAAY,QAAY,OAAO,OAAO,KAAM,OAAO,MAAO,MAAM;AACzF,eAAO,QAAQ,MAAM,UAAY,YAAY,OAAS,KAAK;AAE3D,YAAK,YAAY,MAAO;AAEvB,iBAAO,eAAgB,OAAO,OAAO,MAAO;AAE5C,cAAI;AAEJ,cAAK,OAAO,eAAgB;AAI3B,oBAAQ,KAAM,OAAO,kBAAmB;AACxC,oBAAQ,UAAU;AAElB,gBAAK,OAAO,eAAe;AAAI,sBAAQ,SAAU,SAAS,cAAe,OAAO,UAAW,CAAE;AAE7F,mBAAO,YAAY,UAAW,WAAW,aAAa,MAAO;AAC7D,oBAAQ,YAAa,SAAU;AAC/B,oBAAQ,MAAO,MAAO;AAEtB,oBAAQ,SAAU,CAAE,IAAI;AACxB,oBAAQ,SAAU,CAAE,IAAI;AACxB,oBAAQ,SAAU,EAAG,IAAI;AACzB,oBAAQ,SAAU,EAAG,IAAI;AAEzB,oBAAQ,mBAAoB,OAAQ;AAAA,UAErC,OAAO;AAEN,oBAAQ,mBAAoB,OAAO,WAAY;AAAA,UAEhD;AAEA,gBAAM,UAAU,OAAO;AACvB,gBAAM,eAAe,MAAM,QAAQ,IAAK,MAAO;AAE/C,cAAK,iBAAiB,UAAa,aAAa,UAAU,OAAQ;AAEjE,oBAAQ,MAAM,YAAY;AAE1B,kBAAM,aAAa,EAAE,MAAa;AAClC,kBAAM,QAAQ,IAAK,QAAQ,UAAW;AAAA,UAEvC;AAEA,cAAK,QAAQ,eAAe,eAAgB;AAE3C,0BAAc,YAAa,OAAQ;AAAA,UAEpC;AAEA,iBAAO,cAAe,OAAO,OAAO,MAAO;AAAA,QAE5C;AAAA,MAED;AAEA,eAAU,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,IAAI,GAAG,KAAO;AAE1D,qBAAc,OAAO,SAAU,CAAE,GAAG,OAAO,QAAQ,eAAgB;AAAA,MAEpE;AAAA,IAED;AAAA,EAED;AAED;", "names": []}