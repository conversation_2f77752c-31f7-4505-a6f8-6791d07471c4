{"version": 3, "sources": ["../../tiny-emitter/index.js", "../../tiny-emitter/instance.js"], "sourcesContent": ["function E () {\n  // Keep this empty so it's easier to inherit from\n  // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n}\n\nE.prototype = {\n  on: function (name, callback, ctx) {\n    var e = this.e || (this.e = {});\n\n    (e[name] || (e[name] = [])).push({\n      fn: callback,\n      ctx: ctx\n    });\n\n    return this;\n  },\n\n  once: function (name, callback, ctx) {\n    var self = this;\n    function listener () {\n      self.off(name, listener);\n      callback.apply(ctx, arguments);\n    };\n\n    listener._ = callback\n    return this.on(name, listener, ctx);\n  },\n\n  emit: function (name) {\n    var data = [].slice.call(arguments, 1);\n    var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n    var i = 0;\n    var len = evtArr.length;\n\n    for (i; i < len; i++) {\n      evtArr[i].fn.apply(evtArr[i].ctx, data);\n    }\n\n    return this;\n  },\n\n  off: function (name, callback) {\n    var e = this.e || (this.e = {});\n    var evts = e[name];\n    var liveEvents = [];\n\n    if (evts && callback) {\n      for (var i = 0, len = evts.length; i < len; i++) {\n        if (evts[i].fn !== callback && evts[i].fn._ !== callback)\n          liveEvents.push(evts[i]);\n      }\n    }\n\n    // Remove event from queue to prevent memory leak\n    // Suggested by https://github.com/lazd\n    // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n    (liveEvents.length)\n      ? e[name] = liveEvents\n      : delete e[name];\n\n    return this;\n  }\n};\n\nmodule.exports = E;\nmodule.exports.TinyEmitter = E;\n", "var E = require('./index.js');\nmodule.exports = new E();\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,aAAS,IAAK;AAAA,IAGd;AAEA,MAAE,YAAY;AAAA,MACZ,IAAI,SAAU,MAAM,UAAU,KAAK;AACjC,YAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC;AAE7B,SAAC,EAAE,IAAI,MAAM,EAAE,IAAI,IAAI,CAAC,IAAI,KAAK;AAAA,UAC/B,IAAI;AAAA,UACJ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MAEA,MAAM,SAAU,MAAM,UAAU,KAAK;AACnC,YAAI,OAAO;AACX,iBAAS,WAAY;AACnB,eAAK,IAAI,MAAM,QAAQ;AACvB,mBAAS,MAAM,KAAK,SAAS;AAAA,QAC/B;AAAC;AAED,iBAAS,IAAI;AACb,eAAO,KAAK,GAAG,MAAM,UAAU,GAAG;AAAA,MACpC;AAAA,MAEA,MAAM,SAAU,MAAM;AACpB,YAAI,OAAO,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC;AACrC,YAAI,WAAW,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,MAAM;AAC3D,YAAI,IAAI;AACR,YAAI,MAAM,OAAO;AAEjB,aAAK,GAAG,IAAI,KAAK,KAAK;AACpB,iBAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE,KAAK,IAAI;AAAA,QACxC;AAEA,eAAO;AAAA,MACT;AAAA,MAEA,KAAK,SAAU,MAAM,UAAU;AAC7B,YAAI,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC;AAC7B,YAAI,OAAO,EAAE,IAAI;AACjB,YAAI,aAAa,CAAC;AAElB,YAAI,QAAQ,UAAU;AACpB,mBAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC/C,gBAAI,KAAK,CAAC,EAAE,OAAO,YAAY,KAAK,CAAC,EAAE,GAAG,MAAM;AAC9C,yBAAW,KAAK,KAAK,CAAC,CAAC;AAAA,UAC3B;AAAA,QACF;AAMA,QAAC,WAAW,SACR,EAAE,IAAI,IAAI,aACV,OAAO,EAAE,IAAI;AAEjB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AACjB,WAAO,QAAQ,cAAc;AAAA;AAAA;;;AClE7B;AAAA;AAAA,QAAI,IAAI;AACR,WAAO,UAAU,IAAI,EAAE;AAAA;AAAA;", "names": []}