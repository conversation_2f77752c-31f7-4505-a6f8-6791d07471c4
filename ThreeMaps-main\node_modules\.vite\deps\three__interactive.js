import {
  Ray<PERSON>,
  Vector2
} from "./chunk-XB7L4MI5.js";
import {
  __publicField
} from "./chunk-34KZWS7D.js";

// node_modules/three.interactive/build/three.interactive.js
var c = class {
  constructor(e, n) {
    __publicField(this, "target");
    __publicField(this, "name");
    __publicField(this, "intersected");
    __publicField(this, "wasIntersected", false);
    __publicField(this, "wasIntersectedOnMouseDown", false);
    __publicField(this, "distance");
    this.target = e, this.name = n, this.intersected = false, this.distance = 0;
  }
};
var i = class {
  constructor(e, n = null) {
    __publicField(this, "type");
    __publicField(this, "cancelBubble");
    __publicField(this, "originalEvent");
    __publicField(this, "coords", new Vector2(0, 0));
    __publicField(this, "distance", 0);
    __publicField(this, "intersected", false);
    __publicField(this, "wasIntersected", false);
    __publicField(this, "wasIntersectedOnMouseDown", false);
    this.cancelBubble = false, this.type = e, this.originalEvent = n;
  }
  stopPropagation() {
    this.cancelBubble = true;
  }
};
var a = class {
  constructor(e) {
    __publicField(this, "bindEventsOnBodyElement", true);
    __publicField(this, "autoAdd", false);
    __publicField(this, "scene", null);
    e && typeof e.bindEventsOnBodyElement < "u" && (this.bindEventsOnBodyElement = e.bindEventsOnBodyElement), e && typeof e.scene < "u" && (this.scene = e.scene), e && typeof e.autoAdd < "u" && (this.autoAdd = e.autoAdd);
  }
};
var h = class {
  constructor(e, n, t, s) {
    __publicField(this, "renderer");
    __publicField(this, "camera");
    __publicField(this, "domElement");
    __publicField(this, "bindEventsOnBodyElement");
    __publicField(this, "autoAdd");
    __publicField(this, "scene");
    __publicField(this, "mouse");
    __publicField(this, "supportsPointerEvents");
    __publicField(this, "interactiveObjects");
    __publicField(this, "closestObject");
    __publicField(this, "raycaster");
    __publicField(this, "treatTouchEventsAsMouseEvents");
    __publicField(this, "dispose", () => {
      this.domElement.removeEventListener("click", this.onMouseClick), this.supportsPointerEvents && (this.bindEventsOnBodyElement ? this.domElement.ownerDocument.removeEventListener("pointermove", this.onDocumentPointerMove) : this.domElement.removeEventListener("pointermove", this.onDocumentPointerMove), this.domElement.removeEventListener("pointerdown", this.onPointerDown), this.domElement.removeEventListener("pointerup", this.onPointerUp)), this.bindEventsOnBodyElement ? this.domElement.ownerDocument.removeEventListener("mousemove", this.onDocumentMouseMove) : this.domElement.removeEventListener("mousemove", this.onDocumentMouseMove), this.domElement.removeEventListener("mousedown", this.onMouseDown), this.domElement.removeEventListener("mouseup", this.onMouseUp), this.domElement.removeEventListener("touchstart", this.onTouchStart), this.domElement.removeEventListener("touchmove", this.onTouchMove), this.domElement.removeEventListener("touchend", this.onTouchEnd);
    });
    __publicField(this, "add", (e, n = []) => {
      if (e && !this.interactiveObjects.find((t) => t.target === e))
        if (n.length > 0)
          n.forEach((t) => {
            let s = e.getObjectByName(t);
            if (s) {
              let o = new c(s, t);
              this.interactiveObjects.push(o);
            }
          });
        else {
          let t = new c(e, e.name);
          this.interactiveObjects.push(t);
        }
    });
    __publicField(this, "remove", (e, n = []) => {
      !e || (n.length > 0 ? n.forEach((t) => {
        let s = e.getObjectByName(t);
        s && (this.interactiveObjects = this.interactiveObjects.filter((o) => o.target !== s));
      }) : this.interactiveObjects = this.interactiveObjects.filter((t) => t.target !== e));
    });
    __publicField(this, "update", () => {
      this.raycaster.setFromCamera(this.mouse, this.camera), this.interactiveObjects.forEach((s) => {
        s.target && this.checkIntersection(s);
      }), this.interactiveObjects.sort(function(s, o) {
        return s.distance - o.distance;
      });
      let e = this.interactiveObjects.find((s) => s.intersected) ?? null;
      if (e != this.closestObject) {
        if (this.closestObject) {
          let s = new i("mouseout");
          this.dispatch(this.closestObject, s);
        }
        if (e) {
          let s = new i("mouseover");
          this.dispatch(e, s);
        }
        this.closestObject = e;
      }
      let n;
      this.interactiveObjects.forEach((s) => {
        !s.intersected && s.wasIntersected && (n || (n = new i("mouseleave")), this.dispatch(s, n));
      });
      let t;
      this.interactiveObjects.forEach((s) => {
        s.intersected && !s.wasIntersected && (t || (t = new i("mouseenter")), this.dispatch(s, t));
      });
    });
    __publicField(this, "checkIntersection", (e) => {
      let n = this.raycaster.intersectObjects([e.target], true);
      if (e.wasIntersected = e.intersected, n.length > 0) {
        let t = n[0].distance;
        n.forEach((s) => {
          s.distance < t && (t = s.distance);
        }), e.intersected = true, e.distance = t;
      } else
        e.intersected = false;
    });
    __publicField(this, "onDocumentMouseMove", (e) => {
      this.mapPositionToPoint(this.mouse, e.clientX, e.clientY);
      let n = new i("mousemove", e);
      this.interactiveObjects.forEach((t) => {
        this.dispatch(t, n);
      });
    });
    __publicField(this, "onDocumentPointerMove", (e) => {
      this.mapPositionToPoint(this.mouse, e.clientX, e.clientY);
      let n = new i("pointermove", e);
      this.interactiveObjects.forEach((t) => {
        this.dispatch(t, n);
      });
    });
    __publicField(this, "onTouchMove", (e) => {
      e.touches.length > 0 && this.mapPositionToPoint(this.mouse, e.touches[0].clientX, e.touches[0].clientY);
      let n = new i(this.treatTouchEventsAsMouseEvents ? "mousemove" : "touchmove", e);
      this.interactiveObjects.forEach((t) => {
        this.dispatch(t, n);
      });
    });
    __publicField(this, "onMouseClick", (e) => {
      this.update();
      let n = new i("click", e);
      this.interactiveObjects.forEach((t) => {
        t.intersected && this.dispatch(t, n);
      });
    });
    __publicField(this, "onMouseDown", (e) => {
      this.mapPositionToPoint(this.mouse, e.clientX, e.clientY), this.update();
      let n = new i("mousedown", e);
      this.interactiveObjects.forEach((t) => {
        t.intersected ? (t.wasIntersectedOnMouseDown = true, this.dispatch(t, n)) : t.wasIntersectedOnMouseDown = false;
      });
    });
    __publicField(this, "onPointerDown", (e) => {
      this.mapPositionToPoint(this.mouse, e.clientX, e.clientY), this.update();
      let n = new i("pointerdown", e);
      this.interactiveObjects.forEach((t) => {
        t.intersected && this.dispatch(t, n);
      });
    });
    __publicField(this, "onTouchStart", (e) => {
      e.touches.length > 0 && this.mapPositionToPoint(this.mouse, e.touches[0].clientX, e.touches[0].clientY), this.update();
      let n = new i(this.treatTouchEventsAsMouseEvents ? "mousedown" : "touchstart", e);
      this.interactiveObjects.forEach((t) => {
        t.intersected && this.dispatch(t, n);
      });
    });
    __publicField(this, "onMouseUp", (e) => {
      let n = new i("mouseup", e);
      this.interactiveObjects.forEach((t) => {
        this.dispatch(t, n);
      });
    });
    __publicField(this, "onPointerUp", (e) => {
      let n = new i("pointerup", e);
      this.interactiveObjects.forEach((t) => {
        this.dispatch(t, n);
      });
    });
    __publicField(this, "onTouchEnd", (e) => {
      e.touches.length > 0 && this.mapPositionToPoint(this.mouse, e.touches[0].clientX, e.touches[0].clientY), this.update();
      let n = new i(this.treatTouchEventsAsMouseEvents ? "mouseup" : "touchend", e);
      this.interactiveObjects.forEach((t) => {
        this.dispatch(t, n);
      });
    });
    __publicField(this, "dispatch", (e, n) => {
      e.target && !n.cancelBubble && (n.coords = this.mouse, n.distance = e.distance, n.intersected = e.intersected, n.wasIntersected = e.wasIntersected, n.wasIntersectedOnMouseDown = e.wasIntersectedOnMouseDown, e.target.dispatchEvent(n));
    });
    __publicField(this, "mapPositionToPoint", (e, n, t) => {
      let s = this.renderer.domElement.getBoundingClientRect();
      e.x = (n - s.left) / s.width * 2 - 1, e.y = -((t - s.top) / s.height) * 2 + 1;
    });
    this.renderer = e, this.camera = n, this.domElement = t, this.bindEventsOnBodyElement = s && typeof s.bindEventsOnBodyElement < "u" ? s.bindEventsOnBodyElement : true, this.scene = s && typeof s.scene < "u" ? s.scene : null, this.scene && (this.scene.onBeforeRender = () => {
      this.autoAdd && this.scene !== null && this.scene.traverse((o) => {
        this.add(o), o.addEventListener("removed", (u) => {
          this.remove(u.target);
        });
      }), this.update();
    }), this.autoAdd = s && typeof s.autoAdd < "u" ? s.autoAdd : false, this.autoAdd && this.scene === null && console.error("Attention: Options.scene needs to be set when using options.autoAdd"), this.mouse = new Vector2(-1, 1), this.supportsPointerEvents = !!window.PointerEvent, this.interactiveObjects = [], this.closestObject = null, this.raycaster = new Raycaster(), t.addEventListener("click", this.onMouseClick), this.supportsPointerEvents && (this.bindEventsOnBodyElement ? t.ownerDocument.addEventListener("pointermove", this.onDocumentPointerMove) : t.addEventListener("pointermove", this.onDocumentPointerMove), t.addEventListener("pointerdown", this.onPointerDown), t.addEventListener("pointerup", this.onPointerUp)), this.bindEventsOnBodyElement ? t.ownerDocument.addEventListener("mousemove", this.onDocumentMouseMove) : t.addEventListener("mousemove", this.onDocumentMouseMove), t.addEventListener("mousedown", this.onMouseDown), t.addEventListener("mouseup", this.onMouseUp), t.addEventListener("touchstart", this.onTouchStart, { passive: true }), t.addEventListener("touchmove", this.onTouchMove, { passive: true }), t.addEventListener("touchend", this.onTouchEnd, { passive: true }), this.treatTouchEventsAsMouseEvents = true;
  }
};
export {
  h as InteractionManager,
  a as InteractionManagerOptions,
  i as InteractiveEvent,
  c as InteractiveObject
};
//# sourceMappingURL=three__interactive.js.map
