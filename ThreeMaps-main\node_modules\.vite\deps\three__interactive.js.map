{"version": 3, "sources": ["../../three.interactive/src/index.ts"], "sourcesContent": ["import { Raycaster, Vector2 } from 'three';\n\nexport class InteractiveObject {\n  target: THREE.Object3D;\n  name: string;\n  intersected: boolean;\n  wasIntersected: boolean = false;\n  wasIntersectedOnMouseDown: boolean = false;\n  distance: number;\n  constructor(target: THREE.Object3D, name: string) {\n    this.target = target;\n    this.name = name;\n    this.intersected = false;\n    this.distance = 0;\n  }\n}\n\nexport class InteractiveEvent {\n  type: string;\n  cancelBubble: boolean;\n  originalEvent: Event | null;\n\n  // Dummy default values\n  coords: Vector2 = new Vector2(0, 0);\n  distance: number = 0;\n  intersected: boolean = false;\n  wasIntersected: boolean = false;\n  wasIntersectedOnMouseDown: boolean = false;\n\n  constructor(type: string, originalEvent: Event | null = null) {\n    this.cancelBubble = false;\n    this.type = type;\n    this.originalEvent = originalEvent;\n  }\n  stopPropagation() {\n    this.cancelBubble = true;\n  }\n}\n\nexport class InteractionManagerOptions {\n  bindEventsOnBodyElement: boolean = true;\n  autoAdd: boolean = false;\n  scene: THREE.Scene | null = null;\n\n  constructor(options: {\n    bindEventsOnBodyElement?: boolean | undefined;\n    autoAdd?: boolean | undefined;\n    scene?: THREE.Scene | undefined;\n  }) {\n    if (options && typeof options.bindEventsOnBodyElement !== 'undefined') {\n      this.bindEventsOnBodyElement = options.bindEventsOnBodyElement;\n    }\n    if (options && typeof options.scene !== 'undefined') {\n      this.scene = options.scene;\n    }\n    if (options && typeof options.autoAdd !== 'undefined') {\n      this.autoAdd = options.autoAdd;\n    }\n  }\n}\n\nexport class InteractionManager {\n  renderer: THREE.Renderer;\n  camera: THREE.Camera;\n  domElement: HTMLElement;\n  bindEventsOnBodyElement: boolean;\n  autoAdd: boolean;\n  scene: THREE.Scene | null;\n  mouse: Vector2;\n  supportsPointerEvents: boolean;\n  interactiveObjects: InteractiveObject[];\n  closestObject: InteractiveObject | null;\n  raycaster: THREE.Raycaster;\n  treatTouchEventsAsMouseEvents: boolean;\n\n  constructor(\n    renderer: THREE.Renderer,\n    camera: THREE.Camera,\n    domElement: HTMLElement,\n    options?: InteractionManagerOptions\n  ) {\n    this.renderer = renderer;\n    this.camera = camera;\n    this.domElement = domElement;\n    this.bindEventsOnBodyElement =\n      options && typeof options.bindEventsOnBodyElement !== 'undefined'\n        ? options.bindEventsOnBodyElement\n        : true;\n\n    this.scene =\n      options && typeof options.scene !== 'undefined' ? options.scene : null;\n    if (this.scene) {\n      this.scene.onBeforeRender = () => {\n        if (this.autoAdd && this.scene !== null) {\n          this.scene.traverse((object) => {\n            this.add(object);\n\n            object.addEventListener('removed', (o) => {\n              this.remove(o.target);\n            });\n          });\n        }\n\n        this.update();\n      };\n    }\n    this.autoAdd =\n      options && typeof options.autoAdd !== 'undefined'\n        ? options.autoAdd\n        : false;\n\n    if (this.autoAdd && this.scene === null) {\n      console.error(\n        'Attention: Options.scene needs to be set when using options.autoAdd'\n      );\n    }\n\n    this.mouse = new Vector2(-1, 1); // top left default position\n\n    this.supportsPointerEvents = !!window.PointerEvent;\n\n    this.interactiveObjects = [];\n    this.closestObject = null;\n\n    this.raycaster = new Raycaster();\n\n    domElement.addEventListener('click', this.onMouseClick);\n\n    if (this.supportsPointerEvents) {\n      if (this.bindEventsOnBodyElement) {\n        domElement.ownerDocument.addEventListener(\n          'pointermove',\n          this.onDocumentPointerMove\n        );\n      } else {\n        domElement.addEventListener('pointermove', this.onDocumentPointerMove);\n      }\n      domElement.addEventListener('pointerdown', this.onPointerDown);\n      domElement.addEventListener('pointerup', this.onPointerUp);\n    }\n\n    if (this.bindEventsOnBodyElement) {\n      domElement.ownerDocument.addEventListener(\n        'mousemove',\n        this.onDocumentMouseMove\n      );\n    } else {\n      domElement.addEventListener('mousemove', this.onDocumentMouseMove);\n    }\n    domElement.addEventListener('mousedown', this.onMouseDown);\n    domElement.addEventListener('mouseup', this.onMouseUp);\n    domElement.addEventListener('touchstart', this.onTouchStart, {\n      passive: true,\n    });\n    domElement.addEventListener('touchmove', this.onTouchMove, {\n      passive: true,\n    });\n    domElement.addEventListener('touchend', this.onTouchEnd, {\n      passive: true,\n    });\n\n    this.treatTouchEventsAsMouseEvents = true;\n  }\n\n  dispose = () => {\n    this.domElement.removeEventListener('click', this.onMouseClick);\n\n    if (this.supportsPointerEvents) {\n      if (this.bindEventsOnBodyElement) {\n        this.domElement.ownerDocument.removeEventListener(\n          'pointermove',\n          this.onDocumentPointerMove\n        );\n      } else {\n        this.domElement.removeEventListener(\n          'pointermove',\n          this.onDocumentPointerMove\n        );\n      }\n      this.domElement.removeEventListener('pointerdown', this.onPointerDown);\n      this.domElement.removeEventListener('pointerup', this.onPointerUp);\n    }\n\n    if (this.bindEventsOnBodyElement) {\n      this.domElement.ownerDocument.removeEventListener(\n        'mousemove',\n        this.onDocumentMouseMove\n      );\n    } else {\n      this.domElement.removeEventListener(\n        'mousemove',\n        this.onDocumentMouseMove\n      );\n    }\n    this.domElement.removeEventListener('mousedown', this.onMouseDown);\n    this.domElement.removeEventListener('mouseup', this.onMouseUp);\n    this.domElement.removeEventListener('touchstart', this.onTouchStart);\n    this.domElement.removeEventListener('touchmove', this.onTouchMove);\n    this.domElement.removeEventListener('touchend', this.onTouchEnd);\n  };\n\n  add = (object: THREE.Object3D, childNames: string[] = []) => {\n    if (object && !this.interactiveObjects.find((i) => i.target === object)) {\n      if (childNames.length > 0) {\n        childNames.forEach((name) => {\n          const o = object.getObjectByName(name);\n          if (o) {\n            const interactiveObject = new InteractiveObject(o, name);\n            this.interactiveObjects.push(interactiveObject);\n          }\n        });\n      } else {\n        const interactiveObject = new InteractiveObject(object, object.name);\n        this.interactiveObjects.push(interactiveObject);\n      }\n    }\n  };\n\n  remove = (object: THREE.Object3D, childNames: string[] = []) => {\n    if (!object) return;\n\n    if (childNames.length > 0) {\n      childNames.forEach((name) => {\n        const child = object.getObjectByName(name);\n        if (child) {\n          this.interactiveObjects = this.interactiveObjects.filter(\n            (o) => o.target !== child\n          );\n        }\n      });\n    } else {\n      this.interactiveObjects = this.interactiveObjects.filter(\n        (o) => o.target !== object\n      );\n    }\n  };\n\n  update = () => {\n    this.raycaster.setFromCamera(this.mouse, this.camera);\n\n    this.interactiveObjects.forEach((object) => {\n      if (object.target) this.checkIntersection(object);\n    });\n\n    this.interactiveObjects.sort(function (a, b) {\n      return a.distance - b.distance;\n    });\n\n    const newClosestObject =\n      this.interactiveObjects.find((object) => object.intersected) ?? null;\n    if (newClosestObject != this.closestObject) {\n      if (this.closestObject) {\n        const eventOutClosest = new InteractiveEvent('mouseout');\n        this.dispatch(this.closestObject, eventOutClosest);\n      }\n      if (newClosestObject) {\n        const eventOverClosest = new InteractiveEvent('mouseover');\n        this.dispatch(newClosestObject, eventOverClosest);\n      }\n      this.closestObject = newClosestObject;\n    }\n\n    let eventLeave: InteractiveEvent;\n    this.interactiveObjects.forEach((object) => {\n      if (!object.intersected && object.wasIntersected) {\n        if (!eventLeave) {\n          eventLeave = new InteractiveEvent('mouseleave');\n        }\n        this.dispatch(object, eventLeave);\n      }\n    });\n    let eventEnter: InteractiveEvent;\n    this.interactiveObjects.forEach((object) => {\n      if (object.intersected && !object.wasIntersected) {\n        if (!eventEnter) {\n          eventEnter = new InteractiveEvent('mouseenter');\n        }\n        this.dispatch(object, eventEnter);\n      }\n    });\n  };\n\n  checkIntersection = (object: InteractiveObject) => {\n    const intersects = this.raycaster.intersectObjects([object.target], true);\n\n    object.wasIntersected = object.intersected;\n\n    if (intersects.length > 0) {\n      let distance = intersects[0].distance;\n      intersects.forEach((i) => {\n        if (i.distance < distance) {\n          distance = i.distance;\n        }\n      });\n      object.intersected = true;\n      object.distance = distance;\n    } else {\n      object.intersected = false;\n    }\n  };\n\n  onDocumentMouseMove = (mouseEvent: MouseEvent) => {\n    // event.preventDefault();\n\n    this.mapPositionToPoint(this.mouse, mouseEvent.clientX, mouseEvent.clientY);\n\n    const event = new InteractiveEvent('mousemove', mouseEvent);\n\n    this.interactiveObjects.forEach((object) => {\n      this.dispatch(object, event);\n    });\n  };\n\n  onDocumentPointerMove = (pointerEvent: PointerEvent) => {\n    // event.preventDefault();\n\n    this.mapPositionToPoint(\n      this.mouse,\n      pointerEvent.clientX,\n      pointerEvent.clientY\n    );\n\n    const event = new InteractiveEvent('pointermove', pointerEvent);\n\n    this.interactiveObjects.forEach((object) => {\n      this.dispatch(object, event);\n    });\n  };\n\n  onTouchMove = (touchEvent: TouchEvent) => {\n    // event.preventDefault();\n\n    if (touchEvent.touches.length > 0) {\n      this.mapPositionToPoint(\n        this.mouse,\n        touchEvent.touches[0].clientX,\n        touchEvent.touches[0].clientY\n      );\n    }\n\n    const event = new InteractiveEvent(\n      this.treatTouchEventsAsMouseEvents ? 'mousemove' : 'touchmove',\n      touchEvent\n    );\n\n    this.interactiveObjects.forEach((object) => {\n      this.dispatch(object, event);\n    });\n  };\n\n  onMouseClick = (mouseEvent: MouseEvent) => {\n    this.update();\n\n    const event = new InteractiveEvent('click', mouseEvent);\n\n    this.interactiveObjects.forEach((object) => {\n      if (object.intersected) {\n        this.dispatch(object, event);\n      }\n    });\n  };\n\n  onMouseDown = (mouseEvent: MouseEvent) => {\n    this.mapPositionToPoint(this.mouse, mouseEvent.clientX, mouseEvent.clientY);\n\n    this.update();\n\n    const event = new InteractiveEvent('mousedown', mouseEvent);\n\n    this.interactiveObjects.forEach((object) => {\n      if (object.intersected) {\n        object.wasIntersectedOnMouseDown = true;\n        this.dispatch(object, event);\n      } else {\n        object.wasIntersectedOnMouseDown = false;\n      }\n    });\n  };\n\n  onPointerDown = (pointerEvent: PointerEvent) => {\n    this.mapPositionToPoint(\n      this.mouse,\n      pointerEvent.clientX,\n      pointerEvent.clientY\n    );\n\n    this.update();\n\n    const event = new InteractiveEvent('pointerdown', pointerEvent);\n\n    this.interactiveObjects.forEach((object) => {\n      if (object.intersected) {\n        this.dispatch(object, event);\n      }\n    });\n  };\n\n  onTouchStart = (touchEvent: TouchEvent) => {\n    if (touchEvent.touches.length > 0) {\n      this.mapPositionToPoint(\n        this.mouse,\n        touchEvent.touches[0].clientX,\n        touchEvent.touches[0].clientY\n      );\n    }\n\n    this.update();\n\n    const event = new InteractiveEvent(\n      this.treatTouchEventsAsMouseEvents ? 'mousedown' : 'touchstart',\n      touchEvent\n    );\n\n    this.interactiveObjects.forEach((object) => {\n      if (object.intersected) {\n        this.dispatch(object, event);\n      }\n    });\n  };\n\n  onMouseUp = (mouseEvent: MouseEvent) => {\n    const event = new InteractiveEvent('mouseup', mouseEvent);\n\n    this.interactiveObjects.forEach((object) => {\n      this.dispatch(object, event);\n    });\n  };\n\n  onPointerUp = (pointerEvent: PointerEvent) => {\n    const event = new InteractiveEvent('pointerup', pointerEvent);\n\n    this.interactiveObjects.forEach((object) => {\n      this.dispatch(object, event);\n    });\n  };\n\n  onTouchEnd = (touchEvent: TouchEvent) => {\n    if (touchEvent.touches.length > 0) {\n      this.mapPositionToPoint(\n        this.mouse,\n        touchEvent.touches[0].clientX,\n        touchEvent.touches[0].clientY\n      );\n    }\n\n    this.update();\n\n    const event = new InteractiveEvent(\n      this.treatTouchEventsAsMouseEvents ? 'mouseup' : 'touchend',\n      touchEvent\n    );\n\n    this.interactiveObjects.forEach((object) => {\n      this.dispatch(object, event);\n    });\n  };\n\n  dispatch = (object: InteractiveObject, event: InteractiveEvent) => {\n    if (object.target && !event.cancelBubble) {\n      event.coords = this.mouse;\n      event.distance = object.distance;\n      event.intersected = object.intersected;\n      event.wasIntersected = object.wasIntersected;\n      event.wasIntersectedOnMouseDown = object.wasIntersectedOnMouseDown;\n      object.target.dispatchEvent(event);\n    }\n  };\n\n  mapPositionToPoint = (point: Vector2, x: number, y: number) => {\n    const rect = this.renderer.domElement.getBoundingClientRect();\n\n    point.x = ((x - rect.left) / rect.width) * 2 - 1;\n    point.y = -((y - rect.top) / rect.height) * 2 + 1;\n  };\n}\n"], "mappings": ";;;;;;;;;AAEO,IAAMA,IAAN,MAAwB;EAO7B,YAAYC,GAAwBC,GAAc;AANlD;AACA;AACA;AACA,0CAA0B;AAC1B,qDAAqC;AACrC;AAEE,SAAK,SAASD,GACd,KAAK,OAAOC,GACZ,KAAK,cAAc,OACnB,KAAK,WAAW;EAClB;AACF;AAbO,IAeMC,IAAN,MAAuB;EAY5B,YAAYC,GAAcC,IAA8B,MAAM;AAX9D;AACA;AACA;AAGA,kCAAkB,IAAIC,QAAQ,GAAG,CAAC;AAClC,oCAAmB;AACnB,uCAAuB;AACvB,0CAA0B;AAC1B,qDAAqC;AAGnC,SAAK,eAAe,OACpB,KAAK,OAAOF,GACZ,KAAK,gBAAgBC;EACvB;EACA,kBAAkB;AAChB,SAAK,eAAe;EACtB;AACF;AAnCO,IAqCME,IAAN,MAAgC;EAKrC,YAAYC,GAIT;AARH,mDAAmC;AACnC,mCAAmB;AACnB,iCAA4B;AAOtBA,SAAW,OAAOA,EAAQ,0BAA4B,QACxD,KAAK,0BAA0BA,EAAQ,0BAErCA,KAAW,OAAOA,EAAQ,QAAU,QACtC,KAAK,QAAQA,EAAQ,QAEnBA,KAAW,OAAOA,EAAQ,UAAY,QACxC,KAAK,UAAUA,EAAQ;EAE3B;AACF;AAzDO,IA2DMC,IAAN,MAAyB;EAc9B,YACEC,GACAC,GACAC,GACAJ,GACA;AAlBF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA2FA,mCAAU,MAAM;AACd,WAAK,WAAW,oBAAoB,SAAS,KAAK,YAAY,GAE1D,KAAK,0BACH,KAAK,0BACP,KAAK,WAAW,cAAc,oBAC5B,eACA,KAAK,qBACP,IAEA,KAAK,WAAW,oBACd,eACA,KAAK,qBACP,GAEF,KAAK,WAAW,oBAAoB,eAAe,KAAK,aAAa,GACrE,KAAK,WAAW,oBAAoB,aAAa,KAAK,WAAW,IAG/D,KAAK,0BACP,KAAK,WAAW,cAAc,oBAC5B,aACA,KAAK,mBACP,IAEA,KAAK,WAAW,oBACd,aACA,KAAK,mBACP,GAEF,KAAK,WAAW,oBAAoB,aAAa,KAAK,WAAW,GACjE,KAAK,WAAW,oBAAoB,WAAW,KAAK,SAAS,GAC7D,KAAK,WAAW,oBAAoB,cAAc,KAAK,YAAY,GACnE,KAAK,WAAW,oBAAoB,aAAa,KAAK,WAAW,GACjE,KAAK,WAAW,oBAAoB,YAAY,KAAK,UAAU;IACjE;AAEA,+BAAM,CAACK,GAAwBC,IAAuB,CAAC,MAAM;AAC3D,UAAID,KAAU,CAAC,KAAK,mBAAmB,KAAME,OAAMA,EAAE,WAAWF,CAAM;AACpE,YAAIC,EAAW,SAAS;AACtBA,YAAW,QAASZ,OAAS;AAC3B,gBAAMc,IAAIH,EAAO,gBAAgBX,CAAI;AACrC,gBAAIc,GAAG;AACL,kBAAMC,IAAoB,IAAIjB,EAAkBgB,GAAGd,CAAI;AACvD,mBAAK,mBAAmB,KAAKe,CAAiB;YAChD;UACF,CAAC;aACI;AACL,cAAMA,IAAoB,IAAIjB,EAAkBa,GAAQA,EAAO,IAAI;AACnE,eAAK,mBAAmB,KAAKI,CAAiB;QAChD;IAEJ;AAEA,kCAAS,CAACJ,GAAwBC,IAAuB,CAAC,MAAM;AAC1D,OAACD,MAEDC,EAAW,SAAS,IACtBA,EAAW,QAASZ,OAAS;AAC3B,YAAMgB,IAAQL,EAAO,gBAAgBX,CAAI;AACrCgB,cACF,KAAK,qBAAqB,KAAK,mBAAmB,OAC/C,OAAM,EAAE,WAAWA,CACtB;MAEJ,CAAC,IAED,KAAK,qBAAqB,KAAK,mBAAmB,OAC/CF,OAAMA,EAAE,WAAWH,CACtB;IAEJ;AAEA,kCAAS,MAAM;AACb,WAAK,UAAU,cAAc,KAAK,OAAO,KAAK,MAAM,GAEpD,KAAK,mBAAmB,QAASA,OAAW;AACtCA,UAAO,UAAQ,KAAK,kBAAkBA,CAAM;MAClD,CAAC,GAED,KAAK,mBAAmB,KAAK,SAAUM,GAAGC,GAAG;AAC3C,eAAOD,EAAE,WAAWC,EAAE;MACxB,CAAC;AAED,UAAMC,IACJ,KAAK,mBAAmB,KAAMR,OAAWA,EAAO,WAAW,KAAK;AAClE,UAAIQ,KAAoB,KAAK,eAAe;AAC1C,YAAI,KAAK,eAAe;AACtB,cAAMC,IAAkB,IAAInB,EAAiB,UAAU;AACvD,eAAK,SAAS,KAAK,eAAemB,CAAe;QACnD;AACA,YAAID,GAAkB;AACpB,cAAME,IAAmB,IAAIpB,EAAiB,WAAW;AACzD,eAAK,SAASkB,GAAkBE,CAAgB;QAClD;AACA,aAAK,gBAAgBF;MACvB;AAEA,UAAIG;AACJ,WAAK,mBAAmB,QAASX,OAAW;AACtC,SAACA,EAAO,eAAeA,EAAO,mBAC3BW,MACHA,IAAa,IAAIrB,EAAiB,YAAY,IAEhD,KAAK,SAASU,GAAQW,CAAU;MAEpC,CAAC;AACD,UAAIC;AACJ,WAAK,mBAAmB,QAASZ,OAAW;AACtCA,UAAO,eAAe,CAACA,EAAO,mBAC3BY,MACHA,IAAa,IAAItB,EAAiB,YAAY,IAEhD,KAAK,SAASU,GAAQY,CAAU;MAEpC,CAAC;IACH;AAEA,6CAAqBZ,OAA8B;AACjD,UAAMa,IAAa,KAAK,UAAU,iBAAiB,CAACb,EAAO,MAAM,GAAG,IAAI;AAIxE,UAFAA,EAAO,iBAAiBA,EAAO,aAE3Ba,EAAW,SAAS,GAAG;AACzB,YAAIC,IAAWD,EAAW,CAAA,EAAG;AAC7BA,UAAW,QAASX,OAAM;AACpBA,YAAE,WAAWY,MACfA,IAAWZ,EAAE;QAEjB,CAAC,GACDF,EAAO,cAAc,MACrBA,EAAO,WAAWc;MACpB;AACEd,UAAO,cAAc;IAEzB;AAEA,+CAAuBe,OAA2B;AAGhD,WAAK,mBAAmB,KAAK,OAAOA,EAAW,SAASA,EAAW,OAAO;AAE1E,UAAMC,IAAQ,IAAI1B,EAAiB,aAAayB,CAAU;AAE1D,WAAK,mBAAmB,QAASf,OAAW;AAC1C,aAAK,SAASA,GAAQgB,CAAK;MAC7B,CAAC;IACH;AAEA,iDAAyBC,OAA+B;AAGtD,WAAK,mBACH,KAAK,OACLA,EAAa,SACbA,EAAa,OACf;AAEA,UAAMD,IAAQ,IAAI1B,EAAiB,eAAe2B,CAAY;AAE9D,WAAK,mBAAmB,QAASjB,OAAW;AAC1C,aAAK,SAASA,GAAQgB,CAAK;MAC7B,CAAC;IACH;AAEA,uCAAeE,OAA2B;AAGpCA,QAAW,QAAQ,SAAS,KAC9B,KAAK,mBACH,KAAK,OACLA,EAAW,QAAQ,CAAA,EAAG,SACtBA,EAAW,QAAQ,CAAA,EAAG,OACxB;AAGF,UAAMF,IAAQ,IAAI1B,EAChB,KAAK,gCAAgC,cAAc,aACnD4B,CACF;AAEA,WAAK,mBAAmB,QAASlB,OAAW;AAC1C,aAAK,SAASA,GAAQgB,CAAK;MAC7B,CAAC;IACH;AAEA,wCAAgBD,OAA2B;AACzC,WAAK,OAAO;AAEZ,UAAMC,IAAQ,IAAI1B,EAAiB,SAASyB,CAAU;AAEtD,WAAK,mBAAmB,QAASf,OAAW;AACtCA,UAAO,eACT,KAAK,SAASA,GAAQgB,CAAK;MAE/B,CAAC;IACH;AAEA,uCAAeD,OAA2B;AACxC,WAAK,mBAAmB,KAAK,OAAOA,EAAW,SAASA,EAAW,OAAO,GAE1E,KAAK,OAAO;AAEZ,UAAMC,IAAQ,IAAI1B,EAAiB,aAAayB,CAAU;AAE1D,WAAK,mBAAmB,QAASf,OAAW;AACtCA,UAAO,eACTA,EAAO,4BAA4B,MACnC,KAAK,SAASA,GAAQgB,CAAK,KAE3BhB,EAAO,4BAA4B;MAEvC,CAAC;IACH;AAEA,yCAAiBiB,OAA+B;AAC9C,WAAK,mBACH,KAAK,OACLA,EAAa,SACbA,EAAa,OACf,GAEA,KAAK,OAAO;AAEZ,UAAMD,IAAQ,IAAI1B,EAAiB,eAAe2B,CAAY;AAE9D,WAAK,mBAAmB,QAASjB,OAAW;AACtCA,UAAO,eACT,KAAK,SAASA,GAAQgB,CAAK;MAE/B,CAAC;IACH;AAEA,wCAAgBE,OAA2B;AACrCA,QAAW,QAAQ,SAAS,KAC9B,KAAK,mBACH,KAAK,OACLA,EAAW,QAAQ,CAAA,EAAG,SACtBA,EAAW,QAAQ,CAAA,EAAG,OACxB,GAGF,KAAK,OAAO;AAEZ,UAAMF,IAAQ,IAAI1B,EAChB,KAAK,gCAAgC,cAAc,cACnD4B,CACF;AAEA,WAAK,mBAAmB,QAASlB,OAAW;AACtCA,UAAO,eACT,KAAK,SAASA,GAAQgB,CAAK;MAE/B,CAAC;IACH;AAEA,qCAAaD,OAA2B;AACtC,UAAMC,IAAQ,IAAI1B,EAAiB,WAAWyB,CAAU;AAExD,WAAK,mBAAmB,QAASf,OAAW;AAC1C,aAAK,SAASA,GAAQgB,CAAK;MAC7B,CAAC;IACH;AAEA,uCAAeC,OAA+B;AAC5C,UAAMD,IAAQ,IAAI1B,EAAiB,aAAa2B,CAAY;AAE5D,WAAK,mBAAmB,QAASjB,OAAW;AAC1C,aAAK,SAASA,GAAQgB,CAAK;MAC7B,CAAC;IACH;AAEA,sCAAcE,OAA2B;AACnCA,QAAW,QAAQ,SAAS,KAC9B,KAAK,mBACH,KAAK,OACLA,EAAW,QAAQ,CAAA,EAAG,SACtBA,EAAW,QAAQ,CAAA,EAAG,OACxB,GAGF,KAAK,OAAO;AAEZ,UAAMF,IAAQ,IAAI1B,EAChB,KAAK,gCAAgC,YAAY,YACjD4B,CACF;AAEA,WAAK,mBAAmB,QAASlB,OAAW;AAC1C,aAAK,SAASA,GAAQgB,CAAK;MAC7B,CAAC;IACH;AAEA,oCAAW,CAAChB,GAA2BgB,MAA4B;AAC7DhB,QAAO,UAAU,CAACgB,EAAM,iBAC1BA,EAAM,SAAS,KAAK,OACpBA,EAAM,WAAWhB,EAAO,UACxBgB,EAAM,cAAchB,EAAO,aAC3BgB,EAAM,iBAAiBhB,EAAO,gBAC9BgB,EAAM,4BAA4BhB,EAAO,2BACzCA,EAAO,OAAO,cAAcgB,CAAK;IAErC;AAEA,8CAAqB,CAACG,GAAgBC,GAAWC,MAAc;AAC7D,UAAMC,IAAO,KAAK,SAAS,WAAW,sBAAsB;AAE5DH,QAAM,KAAMC,IAAIE,EAAK,QAAQA,EAAK,QAAS,IAAI,GAC/CH,EAAM,IAAI,GAAGE,IAAIC,EAAK,OAAOA,EAAK,UAAU,IAAI;IAClD;AAxYE,SAAK,WAAWzB,GAChB,KAAK,SAASC,GACd,KAAK,aAAaC,GAClB,KAAK,0BACHJ,KAAW,OAAOA,EAAQ,0BAA4B,MAClDA,EAAQ,0BACR,MAEN,KAAK,QACHA,KAAW,OAAOA,EAAQ,QAAU,MAAcA,EAAQ,QAAQ,MAChE,KAAK,UACP,KAAK,MAAM,iBAAiB,MAAM;AAC5B,WAAK,WAAW,KAAK,UAAU,QACjC,KAAK,MAAM,SAAUK,OAAW;AAC9B,aAAK,IAAIA,CAAM,GAEfA,EAAO,iBAAiB,WAAYG,OAAM;AACxC,eAAK,OAAOA,EAAE,MAAM;QACtB,CAAC;MACH,CAAC,GAGH,KAAK,OAAO;IACd,IAEF,KAAK,UACHR,KAAW,OAAOA,EAAQ,UAAY,MAClCA,EAAQ,UACR,OAEF,KAAK,WAAW,KAAK,UAAU,QACjC,QAAQ,MACN,qEACF,GAGF,KAAK,QAAQ,IAAIF,QAAQ,IAAI,CAAC,GAE9B,KAAK,wBAAwB,CAAC,CAAC,OAAO,cAEtC,KAAK,qBAAqB,CAAC,GAC3B,KAAK,gBAAgB,MAErB,KAAK,YAAY,IAAI8B,aAErBxB,EAAW,iBAAiB,SAAS,KAAK,YAAY,GAElD,KAAK,0BACH,KAAK,0BACPA,EAAW,cAAc,iBACvB,eACA,KAAK,qBACP,IAEAA,EAAW,iBAAiB,eAAe,KAAK,qBAAqB,GAEvEA,EAAW,iBAAiB,eAAe,KAAK,aAAa,GAC7DA,EAAW,iBAAiB,aAAa,KAAK,WAAW,IAGvD,KAAK,0BACPA,EAAW,cAAc,iBACvB,aACA,KAAK,mBACP,IAEAA,EAAW,iBAAiB,aAAa,KAAK,mBAAmB,GAEnEA,EAAW,iBAAiB,aAAa,KAAK,WAAW,GACzDA,EAAW,iBAAiB,WAAW,KAAK,SAAS,GACrDA,EAAW,iBAAiB,cAAc,KAAK,cAAc,EAC3D,SAAS,KACX,CAAC,GACDA,EAAW,iBAAiB,aAAa,KAAK,aAAa,EACzD,SAAS,KACX,CAAC,GACDA,EAAW,iBAAiB,YAAY,KAAK,YAAY,EACvD,SAAS,KACX,CAAC,GAED,KAAK,gCAAgC;EACvC;AAwTF;", "names": ["InteractiveObject", "target", "name", "InteractiveEvent", "type", "originalEvent", "Vector2", "InteractionManagerOptions", "options", "InteractionManager", "renderer", "camera", "dom<PERSON>lement", "object", "child<PERSON><PERSON><PERSON>", "i", "o", "interactiveObject", "child", "a", "b", "newClosestObject", "eventOutClosest", "eventOverClosest", "eventLeave", "eventEnter", "intersects", "distance", "mouseEvent", "event", "pointerEvent", "touchEvent", "point", "x", "y", "rect", "Raycaster"]}