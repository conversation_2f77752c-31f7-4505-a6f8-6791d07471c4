# Nginx 配置文件示例
# 适用于 3D 地图可视化项目

server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名或IP
    
    # 网站根目录 - 指向打包后的 docs 文件夹
    root /var/www/html/threemaps/docs;
    index index.html;
    
    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/octet-stream;
    
    # 主路由 - SPA 单页应用支持
    location / {
        try_files $uri $uri/ /index.html;
        
        # 安全头设置
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }
    
    # 静态资源缓存策略
    location /assets/ {
        # 长期缓存 (1年)
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # 启用 ETag
        etag on;
        
        # 预压缩支持
        gzip_static on;
    }
    
    # 3D 模型文件特殊处理
    location ~* \.(glb|gltf|fbx)$ {
        # 设置正确的 MIME 类型
        add_header Content-Type "application/octet-stream";
        
        # 中期缓存 (1个月)
        expires 1M;
        add_header Cache-Control "public";
        
        # 支持跨域请求 (如果需要)
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Range";
    }
    
    # JSON 地图数据文件
    location ~* \.json$ {
        add_header Content-Type "application/json; charset=utf-8";
        
        # 短期缓存 (1天)
        expires 1d;
        add_header Cache-Control "public";
        
        # 支持跨域
        add_header Access-Control-Allow-Origin "*";
    }
    
    # HDR 环境贴图文件
    location ~* \.hdr$ {
        add_header Content-Type "application/octet-stream";
        expires 1M;
        add_header Cache-Control "public";
    }
    
    # 视频文件处理
    location ~* \.(mp4|mov|webm)$ {
        # 支持 HTTP Range 请求 (视频流)
        add_header Accept-Ranges bytes;
        
        # 中期缓存
        expires 1M;
        add_header Cache-Control "public";
        
        # 视频 MIME 类型
        location ~* \.mp4$ {
            add_header Content-Type "video/mp4";
        }
        location ~* \.mov$ {
            add_header Content-Type "video/quicktime";
        }
        location ~* \.webm$ {
            add_header Content-Type "video/webm";
        }
    }
    
    # 字体文件缓存
    location ~* \.(woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # 图片文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
        expires 1M;
        add_header Cache-Control "public";
        
        # WebP 支持
        location ~* \.webp$ {
            add_header Vary "Accept";
        }
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问配置文件
    location ~* \.(conf|config|env)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    
    # 日志配置
    access_log /var/log/nginx/threemaps_access.log;
    error_log /var/log/nginx/threemaps_error.log;
}

# HTTPS 配置 (可选)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;
#     
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     
#     # SSL 安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     
#     # 其他配置同上...
# }
