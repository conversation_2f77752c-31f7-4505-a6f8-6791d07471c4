{"version": 3, "sources": ["../../three/examples/jsm/lines/LineSegmentsGeometry.js", "../../three/examples/jsm/lines/LineGeometry.js"], "sourcesContent": ["import {\n\tBox3,\n\tFloat32<PERSON><PERSON>er<PERSON>ttribute,\n\tInstancedBufferGeometry,\n\tInstancedInterleavedBuffer,\n\tInterleavedBufferAttribute,\n\tSphere,\n\tVector3,\n\tWireframeGeometry\n} from 'three';\n\nconst _box = new Box3();\nconst _vector = new Vector3();\n\nclass LineSegmentsGeometry extends InstancedBufferGeometry {\n\n\tconstructor() {\n\n\t\tsuper();\n\n\t\tthis.isLineSegmentsGeometry = true;\n\n\t\tthis.type = 'LineSegmentsGeometry';\n\n\t\tconst positions = [ - 1, 2, 0, 1, 2, 0, - 1, 1, 0, 1, 1, 0, - 1, 0, 0, 1, 0, 0, - 1, - 1, 0, 1, - 1, 0 ];\n\t\tconst uvs = [ - 1, 2, 1, 2, - 1, 1, 1, 1, - 1, - 1, 1, - 1, - 1, - 2, 1, - 2 ];\n\t\tconst index = [ 0, 2, 1, 2, 3, 1, 2, 4, 3, 4, 5, 3, 4, 6, 5, 6, 7, 5 ];\n\n\t\tthis.setIndex( index );\n\t\tthis.setAttribute( 'position', new Float32BufferAttribute( positions, 3 ) );\n\t\tthis.setAttribute( 'uv', new Float32BufferAttribute( uvs, 2 ) );\n\n\t}\n\n\tapplyMatrix4( matrix ) {\n\n\t\tconst start = this.attributes.instanceStart;\n\t\tconst end = this.attributes.instanceEnd;\n\n\t\tif ( start !== undefined ) {\n\n\t\t\tstart.applyMatrix4( matrix );\n\n\t\t\tend.applyMatrix4( matrix );\n\n\t\t\tstart.needsUpdate = true;\n\n\t\t}\n\n\t\tif ( this.boundingBox !== null ) {\n\n\t\t\tthis.computeBoundingBox();\n\n\t\t}\n\n\t\tif ( this.boundingSphere !== null ) {\n\n\t\t\tthis.computeBoundingSphere();\n\n\t\t}\n\n\t\treturn this;\n\n\t}\n\n\tsetPositions( array ) {\n\n\t\tlet lineSegments;\n\n\t\tif ( array instanceof Float32Array ) {\n\n\t\t\tlineSegments = array;\n\n\t\t} else if ( Array.isArray( array ) ) {\n\n\t\t\tlineSegments = new Float32Array( array );\n\n\t\t}\n\n\t\tconst instanceBuffer = new InstancedInterleavedBuffer( lineSegments, 6, 1 ); // xyz, xyz\n\n\t\tthis.setAttribute( 'instanceStart', new InterleavedBufferAttribute( instanceBuffer, 3, 0 ) ); // xyz\n\t\tthis.setAttribute( 'instanceEnd', new InterleavedBufferAttribute( instanceBuffer, 3, 3 ) ); // xyz\n\n\t\t//\n\n\t\tthis.computeBoundingBox();\n\t\tthis.computeBoundingSphere();\n\n\t\treturn this;\n\n\t}\n\n\tsetColors( array ) {\n\n\t\tlet colors;\n\n\t\tif ( array instanceof Float32Array ) {\n\n\t\t\tcolors = array;\n\n\t\t} else if ( Array.isArray( array ) ) {\n\n\t\t\tcolors = new Float32Array( array );\n\n\t\t}\n\n\t\tconst instanceColorBuffer = new InstancedInterleavedBuffer( colors, 6, 1 ); // rgb, rgb\n\n\t\tthis.setAttribute( 'instanceColorStart', new InterleavedBufferAttribute( instanceColorBuffer, 3, 0 ) ); // rgb\n\t\tthis.setAttribute( 'instanceColorEnd', new InterleavedBufferAttribute( instanceColorBuffer, 3, 3 ) ); // rgb\n\n\t\treturn this;\n\n\t}\n\n\tfromWireframeGeometry( geometry ) {\n\n\t\tthis.setPositions( geometry.attributes.position.array );\n\n\t\treturn this;\n\n\t}\n\n\tfromEdgesGeometry( geometry ) {\n\n\t\tthis.setPositions( geometry.attributes.position.array );\n\n\t\treturn this;\n\n\t}\n\n\tfromMesh( mesh ) {\n\n\t\tthis.fromWireframeGeometry( new WireframeGeometry( mesh.geometry ) );\n\n\t\t// set colors, maybe\n\n\t\treturn this;\n\n\t}\n\n\tfromLineSegments( lineSegments ) {\n\n\t\tconst geometry = lineSegments.geometry;\n\n\t\tthis.setPositions( geometry.attributes.position.array ); // assumes non-indexed\n\n\t\t// set colors, maybe\n\n\t\treturn this;\n\n\t}\n\n\tcomputeBoundingBox() {\n\n\t\tif ( this.boundingBox === null ) {\n\n\t\t\tthis.boundingBox = new Box3();\n\n\t\t}\n\n\t\tconst start = this.attributes.instanceStart;\n\t\tconst end = this.attributes.instanceEnd;\n\n\t\tif ( start !== undefined && end !== undefined ) {\n\n\t\t\tthis.boundingBox.setFromBufferAttribute( start );\n\n\t\t\t_box.setFromBufferAttribute( end );\n\n\t\t\tthis.boundingBox.union( _box );\n\n\t\t}\n\n\t}\n\n\tcomputeBoundingSphere() {\n\n\t\tif ( this.boundingSphere === null ) {\n\n\t\t\tthis.boundingSphere = new Sphere();\n\n\t\t}\n\n\t\tif ( this.boundingBox === null ) {\n\n\t\t\tthis.computeBoundingBox();\n\n\t\t}\n\n\t\tconst start = this.attributes.instanceStart;\n\t\tconst end = this.attributes.instanceEnd;\n\n\t\tif ( start !== undefined && end !== undefined ) {\n\n\t\t\tconst center = this.boundingSphere.center;\n\n\t\t\tthis.boundingBox.getCenter( center );\n\n\t\t\tlet maxRadiusSq = 0;\n\n\t\t\tfor ( let i = 0, il = start.count; i < il; i ++ ) {\n\n\t\t\t\t_vector.fromBufferAttribute( start, i );\n\t\t\t\tmaxRadiusSq = Math.max( maxRadiusSq, center.distanceToSquared( _vector ) );\n\n\t\t\t\t_vector.fromBufferAttribute( end, i );\n\t\t\t\tmaxRadiusSq = Math.max( maxRadiusSq, center.distanceToSquared( _vector ) );\n\n\t\t\t}\n\n\t\t\tthis.boundingSphere.radius = Math.sqrt( maxRadiusSq );\n\n\t\t\tif ( isNaN( this.boundingSphere.radius ) ) {\n\n\t\t\t\tconsole.error( 'THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.', this );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\ttoJSON() {\n\n\t\t// todo\n\n\t}\n\n\tapplyMatrix( matrix ) {\n\n\t\tconsole.warn( 'THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4().' );\n\n\t\treturn this.applyMatrix4( matrix );\n\n\t}\n\n}\n\nexport { LineSegmentsGeometry };\n", "import { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry.js';\n\nclass LineGeometry extends LineSegmentsGeometry {\n\n\tconstructor() {\n\n\t\tsuper();\n\n\t\tthis.isLineGeometry = true;\n\n\t\tthis.type = 'LineGeometry';\n\n\t}\n\n\tsetPositions( array ) {\n\n\t\t// converts [ x1, y1, z1,  x2, y2, z2, ... ] to pairs format\n\n\t\tconst length = array.length - 3;\n\t\tconst points = new Float32Array( 2 * length );\n\n\t\tfor ( let i = 0; i < length; i += 3 ) {\n\n\t\t\tpoints[ 2 * i ] = array[ i ];\n\t\t\tpoints[ 2 * i + 1 ] = array[ i + 1 ];\n\t\t\tpoints[ 2 * i + 2 ] = array[ i + 2 ];\n\n\t\t\tpoints[ 2 * i + 3 ] = array[ i + 3 ];\n\t\t\tpoints[ 2 * i + 4 ] = array[ i + 4 ];\n\t\t\tpoints[ 2 * i + 5 ] = array[ i + 5 ];\n\n\t\t}\n\n\t\tsuper.setPositions( points );\n\n\t\treturn this;\n\n\t}\n\n\tsetColors( array ) {\n\n\t\t// converts [ r1, g1, b1,  r2, g2, b2, ... ] to pairs format\n\n\t\tconst length = array.length - 3;\n\t\tconst colors = new Float32Array( 2 * length );\n\n\t\tfor ( let i = 0; i < length; i += 3 ) {\n\n\t\t\tcolors[ 2 * i ] = array[ i ];\n\t\t\tcolors[ 2 * i + 1 ] = array[ i + 1 ];\n\t\t\tcolors[ 2 * i + 2 ] = array[ i + 2 ];\n\n\t\t\tcolors[ 2 * i + 3 ] = array[ i + 3 ];\n\t\t\tcolors[ 2 * i + 4 ] = array[ i + 4 ];\n\t\t\tcolors[ 2 * i + 5 ] = array[ i + 5 ];\n\n\t\t}\n\n\t\tsuper.setColors( colors );\n\n\t\treturn this;\n\n\t}\n\n\tfromLine( line ) {\n\n\t\tconst geometry = line.geometry;\n\n\t\tthis.setPositions( geometry.attributes.position.array ); // assumes non-indexed\n\n\t\t// set colors, maybe\n\n\t\treturn this;\n\n\t}\n\n}\n\nexport { LineGeometry };\n"], "mappings": ";;;;;;;;;;;;AAWA,IAAM,OAAO,IAAI,KAAK;AACtB,IAAM,UAAU,IAAI,QAAQ;AAE5B,IAAM,uBAAN,cAAmC,wBAAwB;AAAA,EAE1D,cAAc;AAEb,UAAM;AAEN,SAAK,yBAAyB;AAE9B,SAAK,OAAO;AAEZ,UAAM,YAAY,CAAE,IAAK,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,GAAG,GAAG,GAAG,GAAG,GAAG,IAAK,IAAK,GAAG,GAAG,IAAK,CAAE;AACvG,UAAM,MAAM,CAAE,IAAK,GAAG,GAAG,GAAG,IAAK,GAAG,GAAG,GAAG,IAAK,IAAK,GAAG,IAAK,IAAK,IAAK,GAAG,EAAI;AAC7E,UAAM,QAAQ,CAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAE;AAErE,SAAK,SAAU,KAAM;AACrB,SAAK,aAAc,YAAY,IAAI,uBAAwB,WAAW,CAAE,CAAE;AAC1E,SAAK,aAAc,MAAM,IAAI,uBAAwB,KAAK,CAAE,CAAE;AAAA,EAE/D;AAAA,EAEA,aAAc,QAAS;AAEtB,UAAM,QAAQ,KAAK,WAAW;AAC9B,UAAM,MAAM,KAAK,WAAW;AAE5B,QAAK,UAAU,QAAY;AAE1B,YAAM,aAAc,MAAO;AAE3B,UAAI,aAAc,MAAO;AAEzB,YAAM,cAAc;AAAA,IAErB;AAEA,QAAK,KAAK,gBAAgB,MAAO;AAEhC,WAAK,mBAAmB;AAAA,IAEzB;AAEA,QAAK,KAAK,mBAAmB,MAAO;AAEnC,WAAK,sBAAsB;AAAA,IAE5B;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,aAAc,OAAQ;AAErB,QAAI;AAEJ,QAAK,iBAAiB,cAAe;AAEpC,qBAAe;AAAA,IAEhB,WAAY,MAAM,QAAS,KAAM,GAAI;AAEpC,qBAAe,IAAI,aAAc,KAAM;AAAA,IAExC;AAEA,UAAM,iBAAiB,IAAI,2BAA4B,cAAc,GAAG,CAAE;AAE1E,SAAK,aAAc,iBAAiB,IAAI,2BAA4B,gBAAgB,GAAG,CAAE,CAAE;AAC3F,SAAK,aAAc,eAAe,IAAI,2BAA4B,gBAAgB,GAAG,CAAE,CAAE;AAIzF,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAE3B,WAAO;AAAA,EAER;AAAA,EAEA,UAAW,OAAQ;AAElB,QAAI;AAEJ,QAAK,iBAAiB,cAAe;AAEpC,eAAS;AAAA,IAEV,WAAY,MAAM,QAAS,KAAM,GAAI;AAEpC,eAAS,IAAI,aAAc,KAAM;AAAA,IAElC;AAEA,UAAM,sBAAsB,IAAI,2BAA4B,QAAQ,GAAG,CAAE;AAEzE,SAAK,aAAc,sBAAsB,IAAI,2BAA4B,qBAAqB,GAAG,CAAE,CAAE;AACrG,SAAK,aAAc,oBAAoB,IAAI,2BAA4B,qBAAqB,GAAG,CAAE,CAAE;AAEnG,WAAO;AAAA,EAER;AAAA,EAEA,sBAAuB,UAAW;AAEjC,SAAK,aAAc,SAAS,WAAW,SAAS,KAAM;AAEtD,WAAO;AAAA,EAER;AAAA,EAEA,kBAAmB,UAAW;AAE7B,SAAK,aAAc,SAAS,WAAW,SAAS,KAAM;AAEtD,WAAO;AAAA,EAER;AAAA,EAEA,SAAU,MAAO;AAEhB,SAAK,sBAAuB,IAAI,kBAAmB,KAAK,QAAS,CAAE;AAInE,WAAO;AAAA,EAER;AAAA,EAEA,iBAAkB,cAAe;AAEhC,UAAM,WAAW,aAAa;AAE9B,SAAK,aAAc,SAAS,WAAW,SAAS,KAAM;AAItD,WAAO;AAAA,EAER;AAAA,EAEA,qBAAqB;AAEpB,QAAK,KAAK,gBAAgB,MAAO;AAEhC,WAAK,cAAc,IAAI,KAAK;AAAA,IAE7B;AAEA,UAAM,QAAQ,KAAK,WAAW;AAC9B,UAAM,MAAM,KAAK,WAAW;AAE5B,QAAK,UAAU,UAAa,QAAQ,QAAY;AAE/C,WAAK,YAAY,uBAAwB,KAAM;AAE/C,WAAK,uBAAwB,GAAI;AAEjC,WAAK,YAAY,MAAO,IAAK;AAAA,IAE9B;AAAA,EAED;AAAA,EAEA,wBAAwB;AAEvB,QAAK,KAAK,mBAAmB,MAAO;AAEnC,WAAK,iBAAiB,IAAI,OAAO;AAAA,IAElC;AAEA,QAAK,KAAK,gBAAgB,MAAO;AAEhC,WAAK,mBAAmB;AAAA,IAEzB;AAEA,UAAM,QAAQ,KAAK,WAAW;AAC9B,UAAM,MAAM,KAAK,WAAW;AAE5B,QAAK,UAAU,UAAa,QAAQ,QAAY;AAE/C,YAAM,SAAS,KAAK,eAAe;AAEnC,WAAK,YAAY,UAAW,MAAO;AAEnC,UAAI,cAAc;AAElB,eAAU,IAAI,GAAG,KAAK,MAAM,OAAO,IAAI,IAAI,KAAO;AAEjD,gBAAQ,oBAAqB,OAAO,CAAE;AACtC,sBAAc,KAAK,IAAK,aAAa,OAAO,kBAAmB,OAAQ,CAAE;AAEzE,gBAAQ,oBAAqB,KAAK,CAAE;AACpC,sBAAc,KAAK,IAAK,aAAa,OAAO,kBAAmB,OAAQ,CAAE;AAAA,MAE1E;AAEA,WAAK,eAAe,SAAS,KAAK,KAAM,WAAY;AAEpD,UAAK,MAAO,KAAK,eAAe,MAAO,GAAI;AAE1C,gBAAQ,MAAO,yIAAyI,IAAK;AAAA,MAE9J;AAAA,IAED;AAAA,EAED;AAAA,EAEA,SAAS;AAAA,EAIT;AAAA,EAEA,YAAa,QAAS;AAErB,YAAQ,KAAM,+EAAgF;AAE9F,WAAO,KAAK,aAAc,MAAO;AAAA,EAElC;AAED;;;AC5OA,IAAM,eAAN,cAA2B,qBAAqB;AAAA,EAE/C,cAAc;AAEb,UAAM;AAEN,SAAK,iBAAiB;AAEtB,SAAK,OAAO;AAAA,EAEb;AAAA,EAEA,aAAc,OAAQ;AAIrB,UAAM,SAAS,MAAM,SAAS;AAC9B,UAAM,SAAS,IAAI,aAAc,IAAI,MAAO;AAE5C,aAAU,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAI;AAErC,aAAQ,IAAI,CAAE,IAAI,MAAO,CAAE;AAC3B,aAAQ,IAAI,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACnC,aAAQ,IAAI,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AAEnC,aAAQ,IAAI,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACnC,aAAQ,IAAI,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACnC,aAAQ,IAAI,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AAAA,IAEpC;AAEA,UAAM,aAAc,MAAO;AAE3B,WAAO;AAAA,EAER;AAAA,EAEA,UAAW,OAAQ;AAIlB,UAAM,SAAS,MAAM,SAAS;AAC9B,UAAM,SAAS,IAAI,aAAc,IAAI,MAAO;AAE5C,aAAU,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAI;AAErC,aAAQ,IAAI,CAAE,IAAI,MAAO,CAAE;AAC3B,aAAQ,IAAI,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACnC,aAAQ,IAAI,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AAEnC,aAAQ,IAAI,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACnC,aAAQ,IAAI,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AACnC,aAAQ,IAAI,IAAI,CAAE,IAAI,MAAO,IAAI,CAAE;AAAA,IAEpC;AAEA,UAAM,UAAW,MAAO;AAExB,WAAO;AAAA,EAER;AAAA,EAEA,SAAU,MAAO;AAEhB,UAAM,WAAW,KAAK;AAEtB,SAAK,aAAc,SAAS,WAAW,SAAS,KAAM;AAItD,WAAO;AAAA,EAER;AAED;", "names": []}