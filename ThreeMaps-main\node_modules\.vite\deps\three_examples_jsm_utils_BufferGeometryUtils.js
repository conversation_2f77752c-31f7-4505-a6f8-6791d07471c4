import {
  computeMikkTSpaceTangents,
  computeMorphedAttributes,
  deepCloneAttribute,
  deinterleaveAttribute,
  deinterleaveGeometry,
  estimateBytesUsed,
  interleaveAttributes,
  mergeAttributes,
  mergeGeometries,
  mergeGroups,
  mergeVertices,
  toCreasedNormals,
  toTrianglesDrawMode
} from "./chunk-SOMIYMEL.js";
import "./chunk-XB7L4MI5.js";
import "./chunk-34KZWS7D.js";
export {
  computeMikkTSpaceTangents,
  computeMorphedAttributes,
  deepCloneAttribute,
  deinterleaveAttribute,
  deinterleaveGeometry,
  estimateBytesUsed,
  interleaveAttributes,
  mergeAttributes,
  mergeGeometries,
  mergeGroups,
  mergeVertices,
  toCreasedNormals,
  toTrianglesDrawMode
};
//# sourceMappingURL=three_examples_jsm_utils_BufferGeometryUtils.js.map
