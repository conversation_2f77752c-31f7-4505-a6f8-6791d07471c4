{"version": 3, "sources": ["../../three/examples/jsm/utils/BufferGeometryUtils.js"], "sourcesContent": ["import {\n\tBufferAttribute,\n\tBufferGeometry,\n\tFloat32BufferAttribute,\n\tInstancedBufferAttribute,\n\tInterleavedBuffer,\n\tInterleavedBufferAttribute,\n\tTriangleFanDrawMode,\n\tTriangleStripDrawMode,\n\tTrianglesDrawMode,\n\tVector3,\n} from 'three';\n\nfunction computeMikkTSpaceTangents( geometry, MikkTSpace, negateSign = true ) {\n\n\tif ( ! MikkTSpace || ! MikkTSpace.isReady ) {\n\n\t\tthrow new Error( 'BufferGeometryUtils: Initialized MikkTSpace library required.' );\n\n\t}\n\n\tif ( ! geometry.hasAttribute( 'position' ) || ! geometry.hasAttribute( 'normal' ) || ! geometry.hasAttribute( 'uv' ) ) {\n\n\t\tthrow new Error( 'BufferGeometryUtils: Tangents require \"position\", \"normal\", and \"uv\" attributes.' );\n\n\t}\n\n\tfunction getAttributeArray( attribute ) {\n\n\t\tif ( attribute.normalized || attribute.isInterleavedBufferAttribute ) {\n\n\t\t\tconst dstArray = new Float32Array( attribute.count * attribute.itemSize );\n\n\t\t\tfor ( let i = 0, j = 0; i < attribute.count; i ++ ) {\n\n\t\t\t\tdstArray[ j ++ ] = attribute.getX( i );\n\t\t\t\tdstArray[ j ++ ] = attribute.getY( i );\n\n\t\t\t\tif ( attribute.itemSize > 2 ) {\n\n\t\t\t\t\tdstArray[ j ++ ] = attribute.getZ( i );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn dstArray;\n\n\t\t}\n\n\t\tif ( attribute.array instanceof Float32Array ) {\n\n\t\t\treturn attribute.array;\n\n\t\t}\n\n\t\treturn new Float32Array( attribute.array );\n\n\t}\n\n\t// MikkTSpace algorithm requires non-indexed input.\n\n\tconst _geometry = geometry.index ? geometry.toNonIndexed() : geometry;\n\n\t// Compute vertex tangents.\n\n\tconst tangents = MikkTSpace.generateTangents(\n\n\t\tgetAttributeArray( _geometry.attributes.position ),\n\t\tgetAttributeArray( _geometry.attributes.normal ),\n\t\tgetAttributeArray( _geometry.attributes.uv )\n\n\t);\n\n\t// Texture coordinate convention of glTF differs from the apparent\n\t// default of the MikkTSpace library; .w component must be flipped.\n\n\tif ( negateSign ) {\n\n\t\tfor ( let i = 3; i < tangents.length; i += 4 ) {\n\n\t\t\ttangents[ i ] *= - 1;\n\n\t\t}\n\n\t}\n\n\t//\n\n\t_geometry.setAttribute( 'tangent', new BufferAttribute( tangents, 4 ) );\n\n\tif ( geometry !== _geometry ) {\n\n\t\tgeometry.copy( _geometry );\n\n\t}\n\n\treturn geometry;\n\n}\n\n/**\n * @param  {Array<BufferGeometry>} geometries\n * @param  {Boolean} useGroups\n * @return {BufferGeometry}\n */\nfunction mergeGeometries( geometries, useGroups = false ) {\n\n\tconst isIndexed = geometries[ 0 ].index !== null;\n\n\tconst attributesUsed = new Set( Object.keys( geometries[ 0 ].attributes ) );\n\tconst morphAttributesUsed = new Set( Object.keys( geometries[ 0 ].morphAttributes ) );\n\n\tconst attributes = {};\n\tconst morphAttributes = {};\n\n\tconst morphTargetsRelative = geometries[ 0 ].morphTargetsRelative;\n\n\tconst mergedGeometry = new BufferGeometry();\n\n\tlet offset = 0;\n\n\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\tconst geometry = geometries[ i ];\n\t\tlet attributesCount = 0;\n\n\t\t// ensure that all geometries are indexed, or none\n\n\t\tif ( isIndexed !== ( geometry.index !== null ) ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\t// gather attributes, exit early if they're different\n\n\t\tfor ( const name in geometry.attributes ) {\n\n\t\t\tif ( ! attributesUsed.has( name ) ) {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. All geometries must have compatible attributes; make sure \"' + name + '\" attribute exists among all geometries, or in none of them.' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tif ( attributes[ name ] === undefined ) attributes[ name ] = [];\n\n\t\t\tattributes[ name ].push( geometry.attributes[ name ] );\n\n\t\t\tattributesCount ++;\n\n\t\t}\n\n\t\t// ensure geometries have the same number of attributes\n\n\t\tif ( attributesCount !== attributesUsed.size ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. Make sure all geometries have the same number of attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\t// gather morph attributes, exit early if they're different\n\n\t\tif ( morphTargetsRelative !== geometry.morphTargetsRelative ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. .morphTargetsRelative must be consistent throughout all geometries.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tfor ( const name in geometry.morphAttributes ) {\n\n\t\t\tif ( ! morphAttributesUsed.has( name ) ) {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '.  .morphAttributes must be consistent throughout all geometries.' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tif ( morphAttributes[ name ] === undefined ) morphAttributes[ name ] = [];\n\n\t\t\tmorphAttributes[ name ].push( geometry.morphAttributes[ name ] );\n\n\t\t}\n\n\t\tif ( useGroups ) {\n\n\t\t\tlet count;\n\n\t\t\tif ( isIndexed ) {\n\n\t\t\t\tcount = geometry.index.count;\n\n\t\t\t} else if ( geometry.attributes.position !== undefined ) {\n\n\t\t\t\tcount = geometry.attributes.position.count;\n\n\t\t\t} else {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index ' + i + '. The geometry must have either an index or a position attribute' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tmergedGeometry.addGroup( offset, count, i );\n\n\t\t\toffset += count;\n\n\t\t}\n\n\t}\n\n\t// merge indices\n\n\tif ( isIndexed ) {\n\n\t\tlet indexOffset = 0;\n\t\tconst mergedIndex = [];\n\n\t\tfor ( let i = 0; i < geometries.length; ++ i ) {\n\n\t\t\tconst index = geometries[ i ].index;\n\n\t\t\tfor ( let j = 0; j < index.count; ++ j ) {\n\n\t\t\t\tmergedIndex.push( index.getX( j ) + indexOffset );\n\n\t\t\t}\n\n\t\t\tindexOffset += geometries[ i ].attributes.position.count;\n\n\t\t}\n\n\t\tmergedGeometry.setIndex( mergedIndex );\n\n\t}\n\n\t// merge attributes\n\n\tfor ( const name in attributes ) {\n\n\t\tconst mergedAttribute = mergeAttributes( attributes[ name ] );\n\n\t\tif ( ! mergedAttribute ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the ' + name + ' attribute.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tmergedGeometry.setAttribute( name, mergedAttribute );\n\n\t}\n\n\t// merge morph attributes\n\n\tfor ( const name in morphAttributes ) {\n\n\t\tconst numMorphTargets = morphAttributes[ name ][ 0 ].length;\n\n\t\tif ( numMorphTargets === 0 ) break;\n\n\t\tmergedGeometry.morphAttributes = mergedGeometry.morphAttributes || {};\n\t\tmergedGeometry.morphAttributes[ name ] = [];\n\n\t\tfor ( let i = 0; i < numMorphTargets; ++ i ) {\n\n\t\t\tconst morphAttributesToMerge = [];\n\n\t\t\tfor ( let j = 0; j < morphAttributes[ name ].length; ++ j ) {\n\n\t\t\t\tmorphAttributesToMerge.push( morphAttributes[ name ][ j ][ i ] );\n\n\t\t\t}\n\n\t\t\tconst mergedMorphAttribute = mergeAttributes( morphAttributesToMerge );\n\n\t\t\tif ( ! mergedMorphAttribute ) {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the ' + name + ' morphAttribute.' );\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\tmergedGeometry.morphAttributes[ name ].push( mergedMorphAttribute );\n\n\t\t}\n\n\t}\n\n\treturn mergedGeometry;\n\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {BufferAttribute}\n */\nfunction mergeAttributes( attributes ) {\n\n\tlet TypedArray;\n\tlet itemSize;\n\tlet normalized;\n\tlet gpuType = - 1;\n\tlet arrayLength = 0;\n\n\tfor ( let i = 0; i < attributes.length; ++ i ) {\n\n\t\tconst attribute = attributes[ i ];\n\n\t\tif ( TypedArray === undefined ) TypedArray = attribute.array.constructor;\n\t\tif ( TypedArray !== attribute.array.constructor ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( itemSize === undefined ) itemSize = attribute.itemSize;\n\t\tif ( itemSize !== attribute.itemSize ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( normalized === undefined ) normalized = attribute.normalized;\n\t\tif ( normalized !== attribute.normalized ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tif ( gpuType === - 1 ) gpuType = attribute.gpuType;\n\t\tif ( gpuType !== attribute.gpuType ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.gpuType must be consistent across matching attributes.' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tarrayLength += attribute.count * itemSize;\n\n\t}\n\n\tconst array = new TypedArray( arrayLength );\n\tconst result = new BufferAttribute( array, itemSize, normalized );\n\tlet offset = 0;\n\n\tfor ( let i = 0; i < attributes.length; ++ i ) {\n\n\t\tconst attribute = attributes[ i ];\n\t\tif ( attribute.isInterleavedBufferAttribute ) {\n\n\t\t\tconst tupleOffset = offset / itemSize;\n\t\t\tfor ( let j = 0, l = attribute.count; j < l; j ++ ) {\n\n\t\t\t\tfor ( let c = 0; c < itemSize; c ++ ) {\n\n\t\t\t\t\tconst value = attribute.getComponent( j, c );\n\t\t\t\t\tresult.setComponent( j + tupleOffset, c, value );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tarray.set( attribute.array, offset );\n\n\t\t}\n\n\t\toffset += attribute.count * itemSize;\n\n\t}\n\n\tif ( gpuType !== undefined ) {\n\n\t\tresult.gpuType = gpuType;\n\n\t}\n\n\treturn result;\n\n}\n\n/**\n * @param {BufferAttribute}\n * @return {BufferAttribute}\n */\nexport function deepCloneAttribute( attribute ) {\n\n\tif ( attribute.isInstancedInterleavedBufferAttribute || attribute.isInterleavedBufferAttribute ) {\n\n\t\treturn deinterleaveAttribute( attribute );\n\n\t}\n\n\tif ( attribute.isInstancedBufferAttribute ) {\n\n\t\treturn new InstancedBufferAttribute().copy( attribute );\n\n\t}\n\n\treturn new BufferAttribute().copy( attribute );\n\n}\n\n/**\n * @param {Array<BufferAttribute>} attributes\n * @return {Array<InterleavedBufferAttribute>}\n */\nfunction interleaveAttributes( attributes ) {\n\n\t// Interleaves the provided attributes into an InterleavedBuffer and returns\n\t// a set of InterleavedBufferAttributes for each attribute\n\tlet TypedArray;\n\tlet arrayLength = 0;\n\tlet stride = 0;\n\n\t// calculate the length and type of the interleavedBuffer\n\tfor ( let i = 0, l = attributes.length; i < l; ++ i ) {\n\n\t\tconst attribute = attributes[ i ];\n\n\t\tif ( TypedArray === undefined ) TypedArray = attribute.array.constructor;\n\t\tif ( TypedArray !== attribute.array.constructor ) {\n\n\t\t\tconsole.error( 'AttributeBuffers of different types cannot be interleaved' );\n\t\t\treturn null;\n\n\t\t}\n\n\t\tarrayLength += attribute.array.length;\n\t\tstride += attribute.itemSize;\n\n\t}\n\n\t// Create the set of buffer attributes\n\tconst interleavedBuffer = new InterleavedBuffer( new TypedArray( arrayLength ), stride );\n\tlet offset = 0;\n\tconst res = [];\n\tconst getters = [ 'getX', 'getY', 'getZ', 'getW' ];\n\tconst setters = [ 'setX', 'setY', 'setZ', 'setW' ];\n\n\tfor ( let j = 0, l = attributes.length; j < l; j ++ ) {\n\n\t\tconst attribute = attributes[ j ];\n\t\tconst itemSize = attribute.itemSize;\n\t\tconst count = attribute.count;\n\t\tconst iba = new InterleavedBufferAttribute( interleavedBuffer, itemSize, offset, attribute.normalized );\n\t\tres.push( iba );\n\n\t\toffset += itemSize;\n\n\t\t// Move the data for each attribute into the new interleavedBuffer\n\t\t// at the appropriate offset\n\t\tfor ( let c = 0; c < count; c ++ ) {\n\n\t\t\tfor ( let k = 0; k < itemSize; k ++ ) {\n\n\t\t\t\tiba[ setters[ k ] ]( c, attribute[ getters[ k ] ]( c ) );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\treturn res;\n\n}\n\n// returns a new, non-interleaved version of the provided attribute\nexport function deinterleaveAttribute( attribute ) {\n\n\tconst cons = attribute.data.array.constructor;\n\tconst count = attribute.count;\n\tconst itemSize = attribute.itemSize;\n\tconst normalized = attribute.normalized;\n\n\tconst array = new cons( count * itemSize );\n\tlet newAttribute;\n\tif ( attribute.isInstancedInterleavedBufferAttribute ) {\n\n\t\tnewAttribute = new InstancedBufferAttribute( array, itemSize, normalized, attribute.meshPerAttribute );\n\n\t} else {\n\n\t\tnewAttribute = new BufferAttribute( array, itemSize, normalized );\n\n\t}\n\n\tfor ( let i = 0; i < count; i ++ ) {\n\n\t\tnewAttribute.setX( i, attribute.getX( i ) );\n\n\t\tif ( itemSize >= 2 ) {\n\n\t\t\tnewAttribute.setY( i, attribute.getY( i ) );\n\n\t\t}\n\n\t\tif ( itemSize >= 3 ) {\n\n\t\t\tnewAttribute.setZ( i, attribute.getZ( i ) );\n\n\t\t}\n\n\t\tif ( itemSize >= 4 ) {\n\n\t\t\tnewAttribute.setW( i, attribute.getW( i ) );\n\n\t\t}\n\n\t}\n\n\treturn newAttribute;\n\n}\n\n// deinterleaves all attributes on the geometry\nexport function deinterleaveGeometry( geometry ) {\n\n\tconst attributes = geometry.attributes;\n\tconst morphTargets = geometry.morphTargets;\n\tconst attrMap = new Map();\n\n\tfor ( const key in attributes ) {\n\n\t\tconst attr = attributes[ key ];\n\t\tif ( attr.isInterleavedBufferAttribute ) {\n\n\t\t\tif ( ! attrMap.has( attr ) ) {\n\n\t\t\t\tattrMap.set( attr, deinterleaveAttribute( attr ) );\n\n\t\t\t}\n\n\t\t\tattributes[ key ] = attrMap.get( attr );\n\n\t\t}\n\n\t}\n\n\tfor ( const key in morphTargets ) {\n\n\t\tconst attr = morphTargets[ key ];\n\t\tif ( attr.isInterleavedBufferAttribute ) {\n\n\t\t\tif ( ! attrMap.has( attr ) ) {\n\n\t\t\t\tattrMap.set( attr, deinterleaveAttribute( attr ) );\n\n\t\t\t}\n\n\t\t\tmorphTargets[ key ] = attrMap.get( attr );\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @return {number}\n */\nfunction estimateBytesUsed( geometry ) {\n\n\t// Return the estimated memory used by this geometry in bytes\n\t// Calculate using itemSize, count, and BYTES_PER_ELEMENT to account\n\t// for InterleavedBufferAttributes.\n\tlet mem = 0;\n\tfor ( const name in geometry.attributes ) {\n\n\t\tconst attr = geometry.getAttribute( name );\n\t\tmem += attr.count * attr.itemSize * attr.array.BYTES_PER_ELEMENT;\n\n\t}\n\n\tconst indices = geometry.getIndex();\n\tmem += indices ? indices.count * indices.itemSize * indices.array.BYTES_PER_ELEMENT : 0;\n\treturn mem;\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} tolerance\n * @return {BufferGeometry}\n */\nfunction mergeVertices( geometry, tolerance = 1e-4 ) {\n\n\ttolerance = Math.max( tolerance, Number.EPSILON );\n\n\t// Generate an index buffer if the geometry doesn't have one, or optimize it\n\t// if it's already available.\n\tconst hashToIndex = {};\n\tconst indices = geometry.getIndex();\n\tconst positions = geometry.getAttribute( 'position' );\n\tconst vertexCount = indices ? indices.count : positions.count;\n\n\t// next value for triangle indices\n\tlet nextIndex = 0;\n\n\t// attributes and new attribute arrays\n\tconst attributeNames = Object.keys( geometry.attributes );\n\tconst tmpAttributes = {};\n\tconst tmpMorphAttributes = {};\n\tconst newIndices = [];\n\tconst getters = [ 'getX', 'getY', 'getZ', 'getW' ];\n\tconst setters = [ 'setX', 'setY', 'setZ', 'setW' ];\n\n\t// Initialize the arrays, allocating space conservatively. Extra\n\t// space will be trimmed in the last step.\n\tfor ( let i = 0, l = attributeNames.length; i < l; i ++ ) {\n\n\t\tconst name = attributeNames[ i ];\n\t\tconst attr = geometry.attributes[ name ];\n\n\t\ttmpAttributes[ name ] = new BufferAttribute(\n\t\t\tnew attr.array.constructor( attr.count * attr.itemSize ),\n\t\t\tattr.itemSize,\n\t\t\tattr.normalized\n\t\t);\n\n\t\tconst morphAttr = geometry.morphAttributes[ name ];\n\t\tif ( morphAttr ) {\n\n\t\t\ttmpMorphAttributes[ name ] = new BufferAttribute(\n\t\t\t\tnew morphAttr.array.constructor( morphAttr.count * morphAttr.itemSize ),\n\t\t\t\tmorphAttr.itemSize,\n\t\t\t\tmorphAttr.normalized\n\t\t\t);\n\n\t\t}\n\n\t}\n\n\t// convert the error tolerance to an amount of decimal places to truncate to\n\tconst halfTolerance = tolerance * 0.5;\n\tconst exponent = Math.log10( 1 / tolerance );\n\tconst hashMultiplier = Math.pow( 10, exponent );\n\tconst hashAdditive = halfTolerance * hashMultiplier;\n\tfor ( let i = 0; i < vertexCount; i ++ ) {\n\n\t\tconst index = indices ? indices.getX( i ) : i;\n\n\t\t// Generate a hash for the vertex attributes at the current index 'i'\n\t\tlet hash = '';\n\t\tfor ( let j = 0, l = attributeNames.length; j < l; j ++ ) {\n\n\t\t\tconst name = attributeNames[ j ];\n\t\t\tconst attribute = geometry.getAttribute( name );\n\t\t\tconst itemSize = attribute.itemSize;\n\n\t\t\tfor ( let k = 0; k < itemSize; k ++ ) {\n\n\t\t\t\t// double tilde truncates the decimal value\n\t\t\t\thash += `${ ~ ~ ( attribute[ getters[ k ] ]( index ) * hashMultiplier + hashAdditive ) },`;\n\n\t\t\t}\n\n\t\t}\n\n\t\t// Add another reference to the vertex if it's already\n\t\t// used by another index\n\t\tif ( hash in hashToIndex ) {\n\n\t\t\tnewIndices.push( hashToIndex[ hash ] );\n\n\t\t} else {\n\n\t\t\t// copy data to the new index in the temporary attributes\n\t\t\tfor ( let j = 0, l = attributeNames.length; j < l; j ++ ) {\n\n\t\t\t\tconst name = attributeNames[ j ];\n\t\t\t\tconst attribute = geometry.getAttribute( name );\n\t\t\t\tconst morphAttr = geometry.morphAttributes[ name ];\n\t\t\t\tconst itemSize = attribute.itemSize;\n\t\t\t\tconst newarray = tmpAttributes[ name ];\n\t\t\t\tconst newMorphArrays = tmpMorphAttributes[ name ];\n\n\t\t\t\tfor ( let k = 0; k < itemSize; k ++ ) {\n\n\t\t\t\t\tconst getterFunc = getters[ k ];\n\t\t\t\t\tconst setterFunc = setters[ k ];\n\t\t\t\t\tnewarray[ setterFunc ]( nextIndex, attribute[ getterFunc ]( index ) );\n\n\t\t\t\t\tif ( morphAttr ) {\n\n\t\t\t\t\t\tfor ( let m = 0, ml = morphAttr.length; m < ml; m ++ ) {\n\n\t\t\t\t\t\t\tnewMorphArrays[ m ][ setterFunc ]( nextIndex, morphAttr[ m ][ getterFunc ]( index ) );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\thashToIndex[ hash ] = nextIndex;\n\t\t\tnewIndices.push( nextIndex );\n\t\t\tnextIndex ++;\n\n\t\t}\n\n\t}\n\n\t// generate result BufferGeometry\n\tconst result = geometry.clone();\n\tfor ( const name in geometry.attributes ) {\n\n\t\tconst tmpAttribute = tmpAttributes[ name ];\n\n\t\tresult.setAttribute( name, new BufferAttribute(\n\t\t\ttmpAttribute.array.slice( 0, nextIndex * tmpAttribute.itemSize ),\n\t\t\ttmpAttribute.itemSize,\n\t\t\ttmpAttribute.normalized,\n\t\t) );\n\n\t\tif ( ! ( name in tmpMorphAttributes ) ) continue;\n\n\t\tfor ( let j = 0; j < tmpMorphAttributes[ name ].length; j ++ ) {\n\n\t\t\tconst tmpMorphAttribute = tmpMorphAttributes[ name ][ j ];\n\n\t\t\tresult.morphAttributes[ name ][ j ] = new BufferAttribute(\n\t\t\t\ttmpMorphAttribute.array.slice( 0, nextIndex * tmpMorphAttribute.itemSize ),\n\t\t\t\ttmpMorphAttribute.itemSize,\n\t\t\t\ttmpMorphAttribute.normalized,\n\t\t\t);\n\n\t\t}\n\n\t}\n\n\t// indices\n\n\tresult.setIndex( newIndices );\n\n\treturn result;\n\n}\n\n/**\n * @param {BufferGeometry} geometry\n * @param {number} drawMode\n * @return {BufferGeometry}\n */\nfunction toTrianglesDrawMode( geometry, drawMode ) {\n\n\tif ( drawMode === TrianglesDrawMode ) {\n\n\t\tconsole.warn( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles.' );\n\t\treturn geometry;\n\n\t}\n\n\tif ( drawMode === TriangleFanDrawMode || drawMode === TriangleStripDrawMode ) {\n\n\t\tlet index = geometry.getIndex();\n\n\t\t// generate index if not present\n\n\t\tif ( index === null ) {\n\n\t\t\tconst indices = [];\n\n\t\t\tconst position = geometry.getAttribute( 'position' );\n\n\t\t\tif ( position !== undefined ) {\n\n\t\t\t\tfor ( let i = 0; i < position.count; i ++ ) {\n\n\t\t\t\t\tindices.push( i );\n\n\t\t\t\t}\n\n\t\t\t\tgeometry.setIndex( indices );\n\t\t\t\tindex = geometry.getIndex();\n\n\t\t\t} else {\n\n\t\t\t\tconsole.error( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible.' );\n\t\t\t\treturn geometry;\n\n\t\t\t}\n\n\t\t}\n\n\t\t//\n\n\t\tconst numberOfTriangles = index.count - 2;\n\t\tconst newIndices = [];\n\n\t\tif ( drawMode === TriangleFanDrawMode ) {\n\n\t\t\t// gl.TRIANGLE_FAN\n\n\t\t\tfor ( let i = 1; i <= numberOfTriangles; i ++ ) {\n\n\t\t\t\tnewIndices.push( index.getX( 0 ) );\n\t\t\t\tnewIndices.push( index.getX( i ) );\n\t\t\t\tnewIndices.push( index.getX( i + 1 ) );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\t// gl.TRIANGLE_STRIP\n\n\t\t\tfor ( let i = 0; i < numberOfTriangles; i ++ ) {\n\n\t\t\t\tif ( i % 2 === 0 ) {\n\n\t\t\t\t\tnewIndices.push( index.getX( i ) );\n\t\t\t\t\tnewIndices.push( index.getX( i + 1 ) );\n\t\t\t\t\tnewIndices.push( index.getX( i + 2 ) );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tnewIndices.push( index.getX( i + 2 ) );\n\t\t\t\t\tnewIndices.push( index.getX( i + 1 ) );\n\t\t\t\t\tnewIndices.push( index.getX( i ) );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tif ( ( newIndices.length / 3 ) !== numberOfTriangles ) {\n\n\t\t\tconsole.error( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.' );\n\n\t\t}\n\n\t\t// build final geometry\n\n\t\tconst newGeometry = geometry.clone();\n\t\tnewGeometry.setIndex( newIndices );\n\t\tnewGeometry.clearGroups();\n\n\t\treturn newGeometry;\n\n\t} else {\n\n\t\tconsole.error( 'THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:', drawMode );\n\t\treturn geometry;\n\n\t}\n\n}\n\n/**\n * Calculates the morphed attributes of a morphed/skinned BufferGeometry.\n * Helpful for Raytracing or Decals.\n * @param {Mesh | Line | Points} object An instance of Mesh, Line or Points.\n * @return {Object} An Object with original position/normal attributes and morphed ones.\n */\nfunction computeMorphedAttributes( object ) {\n\n\tconst _vA = new Vector3();\n\tconst _vB = new Vector3();\n\tconst _vC = new Vector3();\n\n\tconst _tempA = new Vector3();\n\tconst _tempB = new Vector3();\n\tconst _tempC = new Vector3();\n\n\tconst _morphA = new Vector3();\n\tconst _morphB = new Vector3();\n\tconst _morphC = new Vector3();\n\n\tfunction _calculateMorphedAttributeData(\n\t\tobject,\n\t\tattribute,\n\t\tmorphAttribute,\n\t\tmorphTargetsRelative,\n\t\ta,\n\t\tb,\n\t\tc,\n\t\tmodifiedAttributeArray\n\t) {\n\n\t\t_vA.fromBufferAttribute( attribute, a );\n\t\t_vB.fromBufferAttribute( attribute, b );\n\t\t_vC.fromBufferAttribute( attribute, c );\n\n\t\tconst morphInfluences = object.morphTargetInfluences;\n\n\t\tif ( morphAttribute && morphInfluences ) {\n\n\t\t\t_morphA.set( 0, 0, 0 );\n\t\t\t_morphB.set( 0, 0, 0 );\n\t\t\t_morphC.set( 0, 0, 0 );\n\n\t\t\tfor ( let i = 0, il = morphAttribute.length; i < il; i ++ ) {\n\n\t\t\t\tconst influence = morphInfluences[ i ];\n\t\t\t\tconst morph = morphAttribute[ i ];\n\n\t\t\t\tif ( influence === 0 ) continue;\n\n\t\t\t\t_tempA.fromBufferAttribute( morph, a );\n\t\t\t\t_tempB.fromBufferAttribute( morph, b );\n\t\t\t\t_tempC.fromBufferAttribute( morph, c );\n\n\t\t\t\tif ( morphTargetsRelative ) {\n\n\t\t\t\t\t_morphA.addScaledVector( _tempA, influence );\n\t\t\t\t\t_morphB.addScaledVector( _tempB, influence );\n\t\t\t\t\t_morphC.addScaledVector( _tempC, influence );\n\n\t\t\t\t} else {\n\n\t\t\t\t\t_morphA.addScaledVector( _tempA.sub( _vA ), influence );\n\t\t\t\t\t_morphB.addScaledVector( _tempB.sub( _vB ), influence );\n\t\t\t\t\t_morphC.addScaledVector( _tempC.sub( _vC ), influence );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t_vA.add( _morphA );\n\t\t\t_vB.add( _morphB );\n\t\t\t_vC.add( _morphC );\n\n\t\t}\n\n\t\tif ( object.isSkinnedMesh ) {\n\n\t\t\tobject.applyBoneTransform( a, _vA );\n\t\t\tobject.applyBoneTransform( b, _vB );\n\t\t\tobject.applyBoneTransform( c, _vC );\n\n\t\t}\n\n\t\tmodifiedAttributeArray[ a * 3 + 0 ] = _vA.x;\n\t\tmodifiedAttributeArray[ a * 3 + 1 ] = _vA.y;\n\t\tmodifiedAttributeArray[ a * 3 + 2 ] = _vA.z;\n\t\tmodifiedAttributeArray[ b * 3 + 0 ] = _vB.x;\n\t\tmodifiedAttributeArray[ b * 3 + 1 ] = _vB.y;\n\t\tmodifiedAttributeArray[ b * 3 + 2 ] = _vB.z;\n\t\tmodifiedAttributeArray[ c * 3 + 0 ] = _vC.x;\n\t\tmodifiedAttributeArray[ c * 3 + 1 ] = _vC.y;\n\t\tmodifiedAttributeArray[ c * 3 + 2 ] = _vC.z;\n\n\t}\n\n\tconst geometry = object.geometry;\n\tconst material = object.material;\n\n\tlet a, b, c;\n\tconst index = geometry.index;\n\tconst positionAttribute = geometry.attributes.position;\n\tconst morphPosition = geometry.morphAttributes.position;\n\tconst morphTargetsRelative = geometry.morphTargetsRelative;\n\tconst normalAttribute = geometry.attributes.normal;\n\tconst morphNormal = geometry.morphAttributes.position;\n\n\tconst groups = geometry.groups;\n\tconst drawRange = geometry.drawRange;\n\tlet i, j, il, jl;\n\tlet group;\n\tlet start, end;\n\n\tconst modifiedPosition = new Float32Array( positionAttribute.count * positionAttribute.itemSize );\n\tconst modifiedNormal = new Float32Array( normalAttribute.count * normalAttribute.itemSize );\n\n\tif ( index !== null ) {\n\n\t\t// indexed buffer geometry\n\n\t\tif ( Array.isArray( material ) ) {\n\n\t\t\tfor ( i = 0, il = groups.length; i < il; i ++ ) {\n\n\t\t\t\tgroup = groups[ i ];\n\n\t\t\t\tstart = Math.max( group.start, drawRange.start );\n\t\t\t\tend = Math.min( ( group.start + group.count ), ( drawRange.start + drawRange.count ) );\n\n\t\t\t\tfor ( j = start, jl = end; j < jl; j += 3 ) {\n\n\t\t\t\t\ta = index.getX( j );\n\t\t\t\t\tb = index.getX( j + 1 );\n\t\t\t\t\tc = index.getX( j + 2 );\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tpositionAttribute,\n\t\t\t\t\t\tmorphPosition,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedPosition\n\t\t\t\t\t);\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tnormalAttribute,\n\t\t\t\t\t\tmorphNormal,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedNormal\n\t\t\t\t\t);\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tstart = Math.max( 0, drawRange.start );\n\t\t\tend = Math.min( index.count, ( drawRange.start + drawRange.count ) );\n\n\t\t\tfor ( i = start, il = end; i < il; i += 3 ) {\n\n\t\t\t\ta = index.getX( i );\n\t\t\t\tb = index.getX( i + 1 );\n\t\t\t\tc = index.getX( i + 2 );\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tpositionAttribute,\n\t\t\t\t\tmorphPosition,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedPosition\n\t\t\t\t);\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tnormalAttribute,\n\t\t\t\t\tmorphNormal,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedNormal\n\t\t\t\t);\n\n\t\t\t}\n\n\t\t}\n\n\t} else {\n\n\t\t// non-indexed buffer geometry\n\n\t\tif ( Array.isArray( material ) ) {\n\n\t\t\tfor ( i = 0, il = groups.length; i < il; i ++ ) {\n\n\t\t\t\tgroup = groups[ i ];\n\n\t\t\t\tstart = Math.max( group.start, drawRange.start );\n\t\t\t\tend = Math.min( ( group.start + group.count ), ( drawRange.start + drawRange.count ) );\n\n\t\t\t\tfor ( j = start, jl = end; j < jl; j += 3 ) {\n\n\t\t\t\t\ta = j;\n\t\t\t\t\tb = j + 1;\n\t\t\t\t\tc = j + 2;\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tpositionAttribute,\n\t\t\t\t\t\tmorphPosition,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedPosition\n\t\t\t\t\t);\n\n\t\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\t\tobject,\n\t\t\t\t\t\tnormalAttribute,\n\t\t\t\t\t\tmorphNormal,\n\t\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\t\ta, b, c,\n\t\t\t\t\t\tmodifiedNormal\n\t\t\t\t\t);\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tstart = Math.max( 0, drawRange.start );\n\t\t\tend = Math.min( positionAttribute.count, ( drawRange.start + drawRange.count ) );\n\n\t\t\tfor ( i = start, il = end; i < il; i += 3 ) {\n\n\t\t\t\ta = i;\n\t\t\t\tb = i + 1;\n\t\t\t\tc = i + 2;\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tpositionAttribute,\n\t\t\t\t\tmorphPosition,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedPosition\n\t\t\t\t);\n\n\t\t\t\t_calculateMorphedAttributeData(\n\t\t\t\t\tobject,\n\t\t\t\t\tnormalAttribute,\n\t\t\t\t\tmorphNormal,\n\t\t\t\t\tmorphTargetsRelative,\n\t\t\t\t\ta, b, c,\n\t\t\t\t\tmodifiedNormal\n\t\t\t\t);\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n\tconst morphedPositionAttribute = new Float32BufferAttribute( modifiedPosition, 3 );\n\tconst morphedNormalAttribute = new Float32BufferAttribute( modifiedNormal, 3 );\n\n\treturn {\n\n\t\tpositionAttribute: positionAttribute,\n\t\tnormalAttribute: normalAttribute,\n\t\tmorphedPositionAttribute: morphedPositionAttribute,\n\t\tmorphedNormalAttribute: morphedNormalAttribute\n\n\t};\n\n}\n\nfunction mergeGroups( geometry ) {\n\n\tif ( geometry.groups.length === 0 ) {\n\n\t\tconsole.warn( 'THREE.BufferGeometryUtils.mergeGroups(): No groups are defined. Nothing to merge.' );\n\t\treturn geometry;\n\n\t}\n\n\tlet groups = geometry.groups;\n\n\t// sort groups by material index\n\n\tgroups = groups.sort( ( a, b ) => {\n\n\t\tif ( a.materialIndex !== b.materialIndex ) return a.materialIndex - b.materialIndex;\n\n\t\treturn a.start - b.start;\n\n\t} );\n\n\t// create index for non-indexed geometries\n\n\tif ( geometry.getIndex() === null ) {\n\n\t\tconst positionAttribute = geometry.getAttribute( 'position' );\n\t\tconst indices = [];\n\n\t\tfor ( let i = 0; i < positionAttribute.count; i += 3 ) {\n\n\t\t\tindices.push( i, i + 1, i + 2 );\n\n\t\t}\n\n\t\tgeometry.setIndex( indices );\n\n\t}\n\n\t// sort index\n\n\tconst index = geometry.getIndex();\n\n\tconst newIndices = [];\n\n\tfor ( let i = 0; i < groups.length; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\n\t\tconst groupStart = group.start;\n\t\tconst groupLength = groupStart + group.count;\n\n\t\tfor ( let j = groupStart; j < groupLength; j ++ ) {\n\n\t\t\tnewIndices.push( index.getX( j ) );\n\n\t\t}\n\n\t}\n\n\tgeometry.dispose(); // Required to force buffer recreation\n\tgeometry.setIndex( newIndices );\n\n\t// update groups indices\n\n\tlet start = 0;\n\n\tfor ( let i = 0; i < groups.length; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\n\t\tgroup.start = start;\n\t\tstart += group.count;\n\n\t}\n\n\t// merge groups\n\n\tlet currentGroup = groups[ 0 ];\n\n\tgeometry.groups = [ currentGroup ];\n\n\tfor ( let i = 1; i < groups.length; i ++ ) {\n\n\t\tconst group = groups[ i ];\n\n\t\tif ( currentGroup.materialIndex === group.materialIndex ) {\n\n\t\t\tcurrentGroup.count += group.count;\n\n\t\t} else {\n\n\t\t\tcurrentGroup = group;\n\t\t\tgeometry.groups.push( currentGroup );\n\n\t\t}\n\n\t}\n\n\treturn geometry;\n\n}\n\n\n/**\n * Modifies the supplied geometry if it is non-indexed, otherwise creates a new,\n * non-indexed geometry. Returns the geometry with smooth normals everywhere except\n * faces that meet at an angle greater than the crease angle.\n *\n * @param {BufferGeometry} geometry\n * @param {number} [creaseAngle]\n * @return {BufferGeometry}\n */\nfunction toCreasedNormals( geometry, creaseAngle = Math.PI / 3 /* 60 degrees */ ) {\n\n\tconst creaseDot = Math.cos( creaseAngle );\n\tconst hashMultiplier = ( 1 + 1e-10 ) * 1e2;\n\n\t// reusable vectors\n\tconst verts = [ new Vector3(), new Vector3(), new Vector3() ];\n\tconst tempVec1 = new Vector3();\n\tconst tempVec2 = new Vector3();\n\tconst tempNorm = new Vector3();\n\tconst tempNorm2 = new Vector3();\n\n\t// hashes a vector\n\tfunction hashVertex( v ) {\n\n\t\tconst x = ~ ~ ( v.x * hashMultiplier );\n\t\tconst y = ~ ~ ( v.y * hashMultiplier );\n\t\tconst z = ~ ~ ( v.z * hashMultiplier );\n\t\treturn `${x},${y},${z}`;\n\n\t}\n\n\t// BufferGeometry.toNonIndexed() warns if the geometry is non-indexed\n\t// and returns the original geometry\n\tconst resultGeometry = geometry.index ? geometry.toNonIndexed() : geometry;\n\tconst posAttr = resultGeometry.attributes.position;\n\tconst vertexMap = {};\n\n\t// find all the normals shared by commonly located vertices\n\tfor ( let i = 0, l = posAttr.count / 3; i < l; i ++ ) {\n\n\t\tconst i3 = 3 * i;\n\t\tconst a = verts[ 0 ].fromBufferAttribute( posAttr, i3 + 0 );\n\t\tconst b = verts[ 1 ].fromBufferAttribute( posAttr, i3 + 1 );\n\t\tconst c = verts[ 2 ].fromBufferAttribute( posAttr, i3 + 2 );\n\n\t\ttempVec1.subVectors( c, b );\n\t\ttempVec2.subVectors( a, b );\n\n\t\t// add the normal to the map for all vertices\n\t\tconst normal = new Vector3().crossVectors( tempVec1, tempVec2 ).normalize();\n\t\tfor ( let n = 0; n < 3; n ++ ) {\n\n\t\t\tconst vert = verts[ n ];\n\t\t\tconst hash = hashVertex( vert );\n\t\t\tif ( ! ( hash in vertexMap ) ) {\n\n\t\t\t\tvertexMap[ hash ] = [];\n\n\t\t\t}\n\n\t\t\tvertexMap[ hash ].push( normal );\n\n\t\t}\n\n\t}\n\n\t// average normals from all vertices that share a common location if they are within the\n\t// provided crease threshold\n\tconst normalArray = new Float32Array( posAttr.count * 3 );\n\tconst normAttr = new BufferAttribute( normalArray, 3, false );\n\tfor ( let i = 0, l = posAttr.count / 3; i < l; i ++ ) {\n\n\t\t// get the face normal for this vertex\n\t\tconst i3 = 3 * i;\n\t\tconst a = verts[ 0 ].fromBufferAttribute( posAttr, i3 + 0 );\n\t\tconst b = verts[ 1 ].fromBufferAttribute( posAttr, i3 + 1 );\n\t\tconst c = verts[ 2 ].fromBufferAttribute( posAttr, i3 + 2 );\n\n\t\ttempVec1.subVectors( c, b );\n\t\ttempVec2.subVectors( a, b );\n\n\t\ttempNorm.crossVectors( tempVec1, tempVec2 ).normalize();\n\n\t\t// average all normals that meet the threshold and set the normal value\n\t\tfor ( let n = 0; n < 3; n ++ ) {\n\n\t\t\tconst vert = verts[ n ];\n\t\t\tconst hash = hashVertex( vert );\n\t\t\tconst otherNormals = vertexMap[ hash ];\n\t\t\ttempNorm2.set( 0, 0, 0 );\n\n\t\t\tfor ( let k = 0, lk = otherNormals.length; k < lk; k ++ ) {\n\n\t\t\t\tconst otherNorm = otherNormals[ k ];\n\t\t\t\tif ( tempNorm.dot( otherNorm ) > creaseDot ) {\n\n\t\t\t\t\ttempNorm2.add( otherNorm );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\ttempNorm2.normalize();\n\t\t\tnormAttr.setXYZ( i3 + n, tempNorm2.x, tempNorm2.y, tempNorm2.z );\n\n\t\t}\n\n\t}\n\n\tresultGeometry.setAttribute( 'normal', normAttr );\n\treturn resultGeometry;\n\n}\n\nexport {\n\tcomputeMikkTSpaceTangents,\n\tmergeGeometries,\n\tmergeAttributes,\n\tinterleaveAttributes,\n\testimateBytesUsed,\n\tmergeVertices,\n\ttoTrianglesDrawMode,\n\tcomputeMorphedAttributes,\n\tmergeGroups,\n\ttoCreasedNormals\n};\n"], "mappings": ";;;;;;;;;;;;;;AAaA,SAAS,0BAA2B,UAAU,YAAY,aAAa,MAAO;AAE7E,MAAK,CAAE,cAAc,CAAE,WAAW,SAAU;AAE3C,UAAM,IAAI,MAAO,+DAAgE;AAAA,EAElF;AAEA,MAAK,CAAE,SAAS,aAAc,UAAW,KAAK,CAAE,SAAS,aAAc,QAAS,KAAK,CAAE,SAAS,aAAc,IAAK,GAAI;AAEtH,UAAM,IAAI,MAAO,kFAAmF;AAAA,EAErG;AAEA,WAAS,kBAAmB,WAAY;AAEvC,QAAK,UAAU,cAAc,UAAU,8BAA+B;AAErE,YAAM,WAAW,IAAI,aAAc,UAAU,QAAQ,UAAU,QAAS;AAExE,eAAU,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,OAAO,KAAO;AAEnD,iBAAU,GAAK,IAAI,UAAU,KAAM,CAAE;AACrC,iBAAU,GAAK,IAAI,UAAU,KAAM,CAAE;AAErC,YAAK,UAAU,WAAW,GAAI;AAE7B,mBAAU,GAAK,IAAI,UAAU,KAAM,CAAE;AAAA,QAEtC;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,QAAK,UAAU,iBAAiB,cAAe;AAE9C,aAAO,UAAU;AAAA,IAElB;AAEA,WAAO,IAAI,aAAc,UAAU,KAAM;AAAA,EAE1C;AAIA,QAAM,YAAY,SAAS,QAAQ,SAAS,aAAa,IAAI;AAI7D,QAAM,WAAW,WAAW;AAAA,IAE3B,kBAAmB,UAAU,WAAW,QAAS;AAAA,IACjD,kBAAmB,UAAU,WAAW,MAAO;AAAA,IAC/C,kBAAmB,UAAU,WAAW,EAAG;AAAA,EAE5C;AAKA,MAAK,YAAa;AAEjB,aAAU,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAI;AAE9C,eAAU,CAAE,KAAK;AAAA,IAElB;AAAA,EAED;AAIA,YAAU,aAAc,WAAW,IAAI,gBAAiB,UAAU,CAAE,CAAE;AAEtE,MAAK,aAAa,WAAY;AAE7B,aAAS,KAAM,SAAU;AAAA,EAE1B;AAEA,SAAO;AAER;AAOA,SAAS,gBAAiB,YAAY,YAAY,OAAQ;AAEzD,QAAM,YAAY,WAAY,CAAE,EAAE,UAAU;AAE5C,QAAM,iBAAiB,IAAI,IAAK,OAAO,KAAM,WAAY,CAAE,EAAE,UAAW,CAAE;AAC1E,QAAM,sBAAsB,IAAI,IAAK,OAAO,KAAM,WAAY,CAAE,EAAE,eAAgB,CAAE;AAEpF,QAAM,aAAa,CAAC;AACpB,QAAM,kBAAkB,CAAC;AAEzB,QAAM,uBAAuB,WAAY,CAAE,EAAE;AAE7C,QAAM,iBAAiB,IAAI,eAAe;AAE1C,MAAI,SAAS;AAEb,WAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAG,GAAI;AAE9C,UAAM,WAAW,WAAY,CAAE;AAC/B,QAAI,kBAAkB;AAItB,QAAK,eAAgB,SAAS,UAAU,OAAS;AAEhD,cAAQ,MAAO,iFAAiF,IAAI,8HAA+H;AACnO,aAAO;AAAA,IAER;AAIA,eAAY,QAAQ,SAAS,YAAa;AAEzC,UAAK,CAAE,eAAe,IAAK,IAAK,GAAI;AAEnC,gBAAQ,MAAO,iFAAiF,IAAI,kEAAkE,OAAO,8DAA+D;AAC5O,eAAO;AAAA,MAER;AAEA,UAAK,WAAY,IAAK,MAAM;AAAY,mBAAY,IAAK,IAAI,CAAC;AAE9D,iBAAY,IAAK,EAAE,KAAM,SAAS,WAAY,IAAK,CAAE;AAErD;AAAA,IAED;AAIA,QAAK,oBAAoB,eAAe,MAAO;AAE9C,cAAQ,MAAO,iFAAiF,IAAI,gEAAiE;AACrK,aAAO;AAAA,IAER;AAIA,QAAK,yBAAyB,SAAS,sBAAuB;AAE7D,cAAQ,MAAO,iFAAiF,IAAI,uEAAwE;AAC5K,aAAO;AAAA,IAER;AAEA,eAAY,QAAQ,SAAS,iBAAkB;AAE9C,UAAK,CAAE,oBAAoB,IAAK,IAAK,GAAI;AAExC,gBAAQ,MAAO,iFAAiF,IAAI,mEAAoE;AACxK,eAAO;AAAA,MAER;AAEA,UAAK,gBAAiB,IAAK,MAAM;AAAY,wBAAiB,IAAK,IAAI,CAAC;AAExE,sBAAiB,IAAK,EAAE,KAAM,SAAS,gBAAiB,IAAK,CAAE;AAAA,IAEhE;AAEA,QAAK,WAAY;AAEhB,UAAI;AAEJ,UAAK,WAAY;AAEhB,gBAAQ,SAAS,MAAM;AAAA,MAExB,WAAY,SAAS,WAAW,aAAa,QAAY;AAExD,gBAAQ,SAAS,WAAW,SAAS;AAAA,MAEtC,OAAO;AAEN,gBAAQ,MAAO,iFAAiF,IAAI,kEAAmE;AACvK,eAAO;AAAA,MAER;AAEA,qBAAe,SAAU,QAAQ,OAAO,CAAE;AAE1C,gBAAU;AAAA,IAEX;AAAA,EAED;AAIA,MAAK,WAAY;AAEhB,QAAI,cAAc;AAClB,UAAM,cAAc,CAAC;AAErB,aAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAG,GAAI;AAE9C,YAAM,QAAQ,WAAY,CAAE,EAAE;AAE9B,eAAU,IAAI,GAAG,IAAI,MAAM,OAAO,EAAG,GAAI;AAExC,oBAAY,KAAM,MAAM,KAAM,CAAE,IAAI,WAAY;AAAA,MAEjD;AAEA,qBAAe,WAAY,CAAE,EAAE,WAAW,SAAS;AAAA,IAEpD;AAEA,mBAAe,SAAU,WAAY;AAAA,EAEtC;AAIA,aAAY,QAAQ,YAAa;AAEhC,UAAM,kBAAkB,gBAAiB,WAAY,IAAK,CAAE;AAE5D,QAAK,CAAE,iBAAkB;AAExB,cAAQ,MAAO,oFAAoF,OAAO,aAAc;AACxH,aAAO;AAAA,IAER;AAEA,mBAAe,aAAc,MAAM,eAAgB;AAAA,EAEpD;AAIA,aAAY,QAAQ,iBAAkB;AAErC,UAAM,kBAAkB,gBAAiB,IAAK,EAAG,CAAE,EAAE;AAErD,QAAK,oBAAoB;AAAI;AAE7B,mBAAe,kBAAkB,eAAe,mBAAmB,CAAC;AACpE,mBAAe,gBAAiB,IAAK,IAAI,CAAC;AAE1C,aAAU,IAAI,GAAG,IAAI,iBAAiB,EAAG,GAAI;AAE5C,YAAM,yBAAyB,CAAC;AAEhC,eAAU,IAAI,GAAG,IAAI,gBAAiB,IAAK,EAAE,QAAQ,EAAG,GAAI;AAE3D,+BAAuB,KAAM,gBAAiB,IAAK,EAAG,CAAE,EAAG,CAAE,CAAE;AAAA,MAEhE;AAEA,YAAM,uBAAuB,gBAAiB,sBAAuB;AAErE,UAAK,CAAE,sBAAuB;AAE7B,gBAAQ,MAAO,oFAAoF,OAAO,kBAAmB;AAC7H,eAAO;AAAA,MAER;AAEA,qBAAe,gBAAiB,IAAK,EAAE,KAAM,oBAAqB;AAAA,IAEnE;AAAA,EAED;AAEA,SAAO;AAER;AAMA,SAAS,gBAAiB,YAAa;AAEtC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,UAAU;AACd,MAAI,cAAc;AAElB,WAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAG,GAAI;AAE9C,UAAM,YAAY,WAAY,CAAE;AAEhC,QAAK,eAAe;AAAY,mBAAa,UAAU,MAAM;AAC7D,QAAK,eAAe,UAAU,MAAM,aAAc;AAEjD,cAAQ,MAAO,2IAA4I;AAC3J,aAAO;AAAA,IAER;AAEA,QAAK,aAAa;AAAY,iBAAW,UAAU;AACnD,QAAK,aAAa,UAAU,UAAW;AAEtC,cAAQ,MAAO,+HAAgI;AAC/I,aAAO;AAAA,IAER;AAEA,QAAK,eAAe;AAAY,mBAAa,UAAU;AACvD,QAAK,eAAe,UAAU,YAAa;AAE1C,cAAQ,MAAO,iIAAkI;AACjJ,aAAO;AAAA,IAER;AAEA,QAAK,YAAY;AAAM,gBAAU,UAAU;AAC3C,QAAK,YAAY,UAAU,SAAU;AAEpC,cAAQ,MAAO,8HAA+H;AAC9I,aAAO;AAAA,IAER;AAEA,mBAAe,UAAU,QAAQ;AAAA,EAElC;AAEA,QAAM,QAAQ,IAAI,WAAY,WAAY;AAC1C,QAAM,SAAS,IAAI,gBAAiB,OAAO,UAAU,UAAW;AAChE,MAAI,SAAS;AAEb,WAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAG,GAAI;AAE9C,UAAM,YAAY,WAAY,CAAE;AAChC,QAAK,UAAU,8BAA+B;AAE7C,YAAM,cAAc,SAAS;AAC7B,eAAU,IAAI,GAAG,IAAI,UAAU,OAAO,IAAI,GAAG,KAAO;AAEnD,iBAAU,IAAI,GAAG,IAAI,UAAU,KAAO;AAErC,gBAAM,QAAQ,UAAU,aAAc,GAAG,CAAE;AAC3C,iBAAO,aAAc,IAAI,aAAa,GAAG,KAAM;AAAA,QAEhD;AAAA,MAED;AAAA,IAED,OAAO;AAEN,YAAM,IAAK,UAAU,OAAO,MAAO;AAAA,IAEpC;AAEA,cAAU,UAAU,QAAQ;AAAA,EAE7B;AAEA,MAAK,YAAY,QAAY;AAE5B,WAAO,UAAU;AAAA,EAElB;AAEA,SAAO;AAER;AAMO,SAAS,mBAAoB,WAAY;AAE/C,MAAK,UAAU,yCAAyC,UAAU,8BAA+B;AAEhG,WAAO,sBAAuB,SAAU;AAAA,EAEzC;AAEA,MAAK,UAAU,4BAA6B;AAE3C,WAAO,IAAI,yBAAyB,EAAE,KAAM,SAAU;AAAA,EAEvD;AAEA,SAAO,IAAI,gBAAgB,EAAE,KAAM,SAAU;AAE9C;AAMA,SAAS,qBAAsB,YAAa;AAI3C,MAAI;AACJ,MAAI,cAAc;AAClB,MAAI,SAAS;AAGb,WAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,EAAG,GAAI;AAErD,UAAM,YAAY,WAAY,CAAE;AAEhC,QAAK,eAAe;AAAY,mBAAa,UAAU,MAAM;AAC7D,QAAK,eAAe,UAAU,MAAM,aAAc;AAEjD,cAAQ,MAAO,2DAA4D;AAC3E,aAAO;AAAA,IAER;AAEA,mBAAe,UAAU,MAAM;AAC/B,cAAU,UAAU;AAAA,EAErB;AAGA,QAAM,oBAAoB,IAAI,kBAAmB,IAAI,WAAY,WAAY,GAAG,MAAO;AACvF,MAAI,SAAS;AACb,QAAM,MAAM,CAAC;AACb,QAAM,UAAU,CAAE,QAAQ,QAAQ,QAAQ,MAAO;AACjD,QAAM,UAAU,CAAE,QAAQ,QAAQ,QAAQ,MAAO;AAEjD,WAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAErD,UAAM,YAAY,WAAY,CAAE;AAChC,UAAM,WAAW,UAAU;AAC3B,UAAM,QAAQ,UAAU;AACxB,UAAM,MAAM,IAAI,2BAA4B,mBAAmB,UAAU,QAAQ,UAAU,UAAW;AACtG,QAAI,KAAM,GAAI;AAEd,cAAU;AAIV,aAAU,IAAI,GAAG,IAAI,OAAO,KAAO;AAElC,eAAU,IAAI,GAAG,IAAI,UAAU,KAAO;AAErC,YAAK,QAAS,CAAE,CAAE,EAAG,GAAG,UAAW,QAAS,CAAE,CAAE,EAAG,CAAE,CAAE;AAAA,MAExD;AAAA,IAED;AAAA,EAED;AAEA,SAAO;AAER;AAGO,SAAS,sBAAuB,WAAY;AAElD,QAAM,OAAO,UAAU,KAAK,MAAM;AAClC,QAAM,QAAQ,UAAU;AACxB,QAAM,WAAW,UAAU;AAC3B,QAAM,aAAa,UAAU;AAE7B,QAAM,QAAQ,IAAI,KAAM,QAAQ,QAAS;AACzC,MAAI;AACJ,MAAK,UAAU,uCAAwC;AAEtD,mBAAe,IAAI,yBAA0B,OAAO,UAAU,YAAY,UAAU,gBAAiB;AAAA,EAEtG,OAAO;AAEN,mBAAe,IAAI,gBAAiB,OAAO,UAAU,UAAW;AAAA,EAEjE;AAEA,WAAU,IAAI,GAAG,IAAI,OAAO,KAAO;AAElC,iBAAa,KAAM,GAAG,UAAU,KAAM,CAAE,CAAE;AAE1C,QAAK,YAAY,GAAI;AAEpB,mBAAa,KAAM,GAAG,UAAU,KAAM,CAAE,CAAE;AAAA,IAE3C;AAEA,QAAK,YAAY,GAAI;AAEpB,mBAAa,KAAM,GAAG,UAAU,KAAM,CAAE,CAAE;AAAA,IAE3C;AAEA,QAAK,YAAY,GAAI;AAEpB,mBAAa,KAAM,GAAG,UAAU,KAAM,CAAE,CAAE;AAAA,IAE3C;AAAA,EAED;AAEA,SAAO;AAER;AAGO,SAAS,qBAAsB,UAAW;AAEhD,QAAM,aAAa,SAAS;AAC5B,QAAM,eAAe,SAAS;AAC9B,QAAM,UAAU,oBAAI,IAAI;AAExB,aAAY,OAAO,YAAa;AAE/B,UAAM,OAAO,WAAY,GAAI;AAC7B,QAAK,KAAK,8BAA+B;AAExC,UAAK,CAAE,QAAQ,IAAK,IAAK,GAAI;AAE5B,gBAAQ,IAAK,MAAM,sBAAuB,IAAK,CAAE;AAAA,MAElD;AAEA,iBAAY,GAAI,IAAI,QAAQ,IAAK,IAAK;AAAA,IAEvC;AAAA,EAED;AAEA,aAAY,OAAO,cAAe;AAEjC,UAAM,OAAO,aAAc,GAAI;AAC/B,QAAK,KAAK,8BAA+B;AAExC,UAAK,CAAE,QAAQ,IAAK,IAAK,GAAI;AAE5B,gBAAQ,IAAK,MAAM,sBAAuB,IAAK,CAAE;AAAA,MAElD;AAEA,mBAAc,GAAI,IAAI,QAAQ,IAAK,IAAK;AAAA,IAEzC;AAAA,EAED;AAED;AAMA,SAAS,kBAAmB,UAAW;AAKtC,MAAI,MAAM;AACV,aAAY,QAAQ,SAAS,YAAa;AAEzC,UAAM,OAAO,SAAS,aAAc,IAAK;AACzC,WAAO,KAAK,QAAQ,KAAK,WAAW,KAAK,MAAM;AAAA,EAEhD;AAEA,QAAM,UAAU,SAAS,SAAS;AAClC,SAAO,UAAU,QAAQ,QAAQ,QAAQ,WAAW,QAAQ,MAAM,oBAAoB;AACtF,SAAO;AAER;AAOA,SAAS,cAAe,UAAU,YAAY,MAAO;AAEpD,cAAY,KAAK,IAAK,WAAW,OAAO,OAAQ;AAIhD,QAAM,cAAc,CAAC;AACrB,QAAM,UAAU,SAAS,SAAS;AAClC,QAAM,YAAY,SAAS,aAAc,UAAW;AACpD,QAAM,cAAc,UAAU,QAAQ,QAAQ,UAAU;AAGxD,MAAI,YAAY;AAGhB,QAAM,iBAAiB,OAAO,KAAM,SAAS,UAAW;AACxD,QAAM,gBAAgB,CAAC;AACvB,QAAM,qBAAqB,CAAC;AAC5B,QAAM,aAAa,CAAC;AACpB,QAAM,UAAU,CAAE,QAAQ,QAAQ,QAAQ,MAAO;AACjD,QAAM,UAAU,CAAE,QAAQ,QAAQ,QAAQ,MAAO;AAIjD,WAAU,IAAI,GAAG,IAAI,eAAe,QAAQ,IAAI,GAAG,KAAO;AAEzD,UAAM,OAAO,eAAgB,CAAE;AAC/B,UAAM,OAAO,SAAS,WAAY,IAAK;AAEvC,kBAAe,IAAK,IAAI,IAAI;AAAA,MAC3B,IAAI,KAAK,MAAM,YAAa,KAAK,QAAQ,KAAK,QAAS;AAAA,MACvD,KAAK;AAAA,MACL,KAAK;AAAA,IACN;AAEA,UAAM,YAAY,SAAS,gBAAiB,IAAK;AACjD,QAAK,WAAY;AAEhB,yBAAoB,IAAK,IAAI,IAAI;AAAA,QAChC,IAAI,UAAU,MAAM,YAAa,UAAU,QAAQ,UAAU,QAAS;AAAA,QACtE,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAAA,IAED;AAAA,EAED;AAGA,QAAM,gBAAgB,YAAY;AAClC,QAAM,WAAW,KAAK,MAAO,IAAI,SAAU;AAC3C,QAAM,iBAAiB,KAAK,IAAK,IAAI,QAAS;AAC9C,QAAM,eAAe,gBAAgB;AACrC,WAAU,IAAI,GAAG,IAAI,aAAa,KAAO;AAExC,UAAM,QAAQ,UAAU,QAAQ,KAAM,CAAE,IAAI;AAG5C,QAAI,OAAO;AACX,aAAU,IAAI,GAAG,IAAI,eAAe,QAAQ,IAAI,GAAG,KAAO;AAEzD,YAAM,OAAO,eAAgB,CAAE;AAC/B,YAAM,YAAY,SAAS,aAAc,IAAK;AAC9C,YAAM,WAAW,UAAU;AAE3B,eAAU,IAAI,GAAG,IAAI,UAAU,KAAO;AAGrC,gBAAQ,GAAI,CAAE,EAAI,UAAW,QAAS,CAAE,CAAE,EAAG,KAAM,IAAI,iBAAiB,aAAe;AAAA,MAExF;AAAA,IAED;AAIA,QAAK,QAAQ,aAAc;AAE1B,iBAAW,KAAM,YAAa,IAAK,CAAE;AAAA,IAEtC,OAAO;AAGN,eAAU,IAAI,GAAG,IAAI,eAAe,QAAQ,IAAI,GAAG,KAAO;AAEzD,cAAM,OAAO,eAAgB,CAAE;AAC/B,cAAM,YAAY,SAAS,aAAc,IAAK;AAC9C,cAAM,YAAY,SAAS,gBAAiB,IAAK;AACjD,cAAM,WAAW,UAAU;AAC3B,cAAM,WAAW,cAAe,IAAK;AACrC,cAAM,iBAAiB,mBAAoB,IAAK;AAEhD,iBAAU,IAAI,GAAG,IAAI,UAAU,KAAO;AAErC,gBAAM,aAAa,QAAS,CAAE;AAC9B,gBAAM,aAAa,QAAS,CAAE;AAC9B,mBAAU,UAAW,EAAG,WAAW,UAAW,UAAW,EAAG,KAAM,CAAE;AAEpE,cAAK,WAAY;AAEhB,qBAAU,IAAI,GAAG,KAAK,UAAU,QAAQ,IAAI,IAAI,KAAO;AAEtD,6BAAgB,CAAE,EAAG,UAAW,EAAG,WAAW,UAAW,CAAE,EAAG,UAAW,EAAG,KAAM,CAAE;AAAA,YAErF;AAAA,UAED;AAAA,QAED;AAAA,MAED;AAEA,kBAAa,IAAK,IAAI;AACtB,iBAAW,KAAM,SAAU;AAC3B;AAAA,IAED;AAAA,EAED;AAGA,QAAM,SAAS,SAAS,MAAM;AAC9B,aAAY,QAAQ,SAAS,YAAa;AAEzC,UAAM,eAAe,cAAe,IAAK;AAEzC,WAAO,aAAc,MAAM,IAAI;AAAA,MAC9B,aAAa,MAAM,MAAO,GAAG,YAAY,aAAa,QAAS;AAAA,MAC/D,aAAa;AAAA,MACb,aAAa;AAAA,IACd,CAAE;AAEF,QAAK,EAAI,QAAQ;AAAuB;AAExC,aAAU,IAAI,GAAG,IAAI,mBAAoB,IAAK,EAAE,QAAQ,KAAO;AAE9D,YAAM,oBAAoB,mBAAoB,IAAK,EAAG,CAAE;AAExD,aAAO,gBAAiB,IAAK,EAAG,CAAE,IAAI,IAAI;AAAA,QACzC,kBAAkB,MAAM,MAAO,GAAG,YAAY,kBAAkB,QAAS;AAAA,QACzE,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,MACnB;AAAA,IAED;AAAA,EAED;AAIA,SAAO,SAAU,UAAW;AAE5B,SAAO;AAER;AAOA,SAAS,oBAAqB,UAAU,UAAW;AAElD,MAAK,aAAa,mBAAoB;AAErC,YAAQ,KAAM,yFAA0F;AACxG,WAAO;AAAA,EAER;AAEA,MAAK,aAAa,uBAAuB,aAAa,uBAAwB;AAE7E,QAAI,QAAQ,SAAS,SAAS;AAI9B,QAAK,UAAU,MAAO;AAErB,YAAM,UAAU,CAAC;AAEjB,YAAM,WAAW,SAAS,aAAc,UAAW;AAEnD,UAAK,aAAa,QAAY;AAE7B,iBAAU,IAAI,GAAG,IAAI,SAAS,OAAO,KAAO;AAE3C,kBAAQ,KAAM,CAAE;AAAA,QAEjB;AAEA,iBAAS,SAAU,OAAQ;AAC3B,gBAAQ,SAAS,SAAS;AAAA,MAE3B,OAAO;AAEN,gBAAQ,MAAO,yGAA0G;AACzH,eAAO;AAAA,MAER;AAAA,IAED;AAIA,UAAM,oBAAoB,MAAM,QAAQ;AACxC,UAAM,aAAa,CAAC;AAEpB,QAAK,aAAa,qBAAsB;AAIvC,eAAU,IAAI,GAAG,KAAK,mBAAmB,KAAO;AAE/C,mBAAW,KAAM,MAAM,KAAM,CAAE,CAAE;AACjC,mBAAW,KAAM,MAAM,KAAM,CAAE,CAAE;AACjC,mBAAW,KAAM,MAAM,KAAM,IAAI,CAAE,CAAE;AAAA,MAEtC;AAAA,IAED,OAAO;AAIN,eAAU,IAAI,GAAG,IAAI,mBAAmB,KAAO;AAE9C,YAAK,IAAI,MAAM,GAAI;AAElB,qBAAW,KAAM,MAAM,KAAM,CAAE,CAAE;AACjC,qBAAW,KAAM,MAAM,KAAM,IAAI,CAAE,CAAE;AACrC,qBAAW,KAAM,MAAM,KAAM,IAAI,CAAE,CAAE;AAAA,QAEtC,OAAO;AAEN,qBAAW,KAAM,MAAM,KAAM,IAAI,CAAE,CAAE;AACrC,qBAAW,KAAM,MAAM,KAAM,IAAI,CAAE,CAAE;AACrC,qBAAW,KAAM,MAAM,KAAM,CAAE,CAAE;AAAA,QAElC;AAAA,MAED;AAAA,IAED;AAEA,QAAO,WAAW,SAAS,MAAQ,mBAAoB;AAEtD,cAAQ,MAAO,kGAAmG;AAAA,IAEnH;AAIA,UAAM,cAAc,SAAS,MAAM;AACnC,gBAAY,SAAU,UAAW;AACjC,gBAAY,YAAY;AAExB,WAAO;AAAA,EAER,OAAO;AAEN,YAAQ,MAAO,uEAAuE,QAAS;AAC/F,WAAO;AAAA,EAER;AAED;AAQA,SAAS,yBAA0B,QAAS;AAE3C,QAAM,MAAM,IAAI,QAAQ;AACxB,QAAM,MAAM,IAAI,QAAQ;AACxB,QAAM,MAAM,IAAI,QAAQ;AAExB,QAAM,SAAS,IAAI,QAAQ;AAC3B,QAAM,SAAS,IAAI,QAAQ;AAC3B,QAAM,SAAS,IAAI,QAAQ;AAE3B,QAAM,UAAU,IAAI,QAAQ;AAC5B,QAAM,UAAU,IAAI,QAAQ;AAC5B,QAAM,UAAU,IAAI,QAAQ;AAE5B,WAAS,+BACRA,SACA,WACA,gBACAC,uBACAC,IACAC,IACAC,IACA,wBACC;AAED,QAAI,oBAAqB,WAAWF,EAAE;AACtC,QAAI,oBAAqB,WAAWC,EAAE;AACtC,QAAI,oBAAqB,WAAWC,EAAE;AAEtC,UAAM,kBAAkBJ,QAAO;AAE/B,QAAK,kBAAkB,iBAAkB;AAExC,cAAQ,IAAK,GAAG,GAAG,CAAE;AACrB,cAAQ,IAAK,GAAG,GAAG,CAAE;AACrB,cAAQ,IAAK,GAAG,GAAG,CAAE;AAErB,eAAUK,KAAI,GAAGC,MAAK,eAAe,QAAQD,KAAIC,KAAID,MAAO;AAE3D,cAAM,YAAY,gBAAiBA,EAAE;AACrC,cAAM,QAAQ,eAAgBA,EAAE;AAEhC,YAAK,cAAc;AAAI;AAEvB,eAAO,oBAAqB,OAAOH,EAAE;AACrC,eAAO,oBAAqB,OAAOC,EAAE;AACrC,eAAO,oBAAqB,OAAOC,EAAE;AAErC,YAAKH,uBAAuB;AAE3B,kBAAQ,gBAAiB,QAAQ,SAAU;AAC3C,kBAAQ,gBAAiB,QAAQ,SAAU;AAC3C,kBAAQ,gBAAiB,QAAQ,SAAU;AAAA,QAE5C,OAAO;AAEN,kBAAQ,gBAAiB,OAAO,IAAK,GAAI,GAAG,SAAU;AACtD,kBAAQ,gBAAiB,OAAO,IAAK,GAAI,GAAG,SAAU;AACtD,kBAAQ,gBAAiB,OAAO,IAAK,GAAI,GAAG,SAAU;AAAA,QAEvD;AAAA,MAED;AAEA,UAAI,IAAK,OAAQ;AACjB,UAAI,IAAK,OAAQ;AACjB,UAAI,IAAK,OAAQ;AAAA,IAElB;AAEA,QAAKD,QAAO,eAAgB;AAE3B,MAAAA,QAAO,mBAAoBE,IAAG,GAAI;AAClC,MAAAF,QAAO,mBAAoBG,IAAG,GAAI;AAClC,MAAAH,QAAO,mBAAoBI,IAAG,GAAI;AAAA,IAEnC;AAEA,2BAAwBF,KAAI,IAAI,CAAE,IAAI,IAAI;AAC1C,2BAAwBA,KAAI,IAAI,CAAE,IAAI,IAAI;AAC1C,2BAAwBA,KAAI,IAAI,CAAE,IAAI,IAAI;AAC1C,2BAAwBC,KAAI,IAAI,CAAE,IAAI,IAAI;AAC1C,2BAAwBA,KAAI,IAAI,CAAE,IAAI,IAAI;AAC1C,2BAAwBA,KAAI,IAAI,CAAE,IAAI,IAAI;AAC1C,2BAAwBC,KAAI,IAAI,CAAE,IAAI,IAAI;AAC1C,2BAAwBA,KAAI,IAAI,CAAE,IAAI,IAAI;AAC1C,2BAAwBA,KAAI,IAAI,CAAE,IAAI,IAAI;AAAA,EAE3C;AAEA,QAAM,WAAW,OAAO;AACxB,QAAM,WAAW,OAAO;AAExB,MAAI,GAAG,GAAG;AACV,QAAM,QAAQ,SAAS;AACvB,QAAM,oBAAoB,SAAS,WAAW;AAC9C,QAAM,gBAAgB,SAAS,gBAAgB;AAC/C,QAAM,uBAAuB,SAAS;AACtC,QAAM,kBAAkB,SAAS,WAAW;AAC5C,QAAM,cAAc,SAAS,gBAAgB;AAE7C,QAAM,SAAS,SAAS;AACxB,QAAM,YAAY,SAAS;AAC3B,MAAI,GAAG,GAAG,IAAI;AACd,MAAI;AACJ,MAAI,OAAO;AAEX,QAAM,mBAAmB,IAAI,aAAc,kBAAkB,QAAQ,kBAAkB,QAAS;AAChG,QAAM,iBAAiB,IAAI,aAAc,gBAAgB,QAAQ,gBAAgB,QAAS;AAE1F,MAAK,UAAU,MAAO;AAIrB,QAAK,MAAM,QAAS,QAAS,GAAI;AAEhC,WAAM,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAO;AAE/C,gBAAQ,OAAQ,CAAE;AAElB,gBAAQ,KAAK,IAAK,MAAM,OAAO,UAAU,KAAM;AAC/C,cAAM,KAAK,IAAO,MAAM,QAAQ,MAAM,OAAW,UAAU,QAAQ,UAAU,KAAQ;AAErF,aAAM,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,GAAI;AAE3C,cAAI,MAAM,KAAM,CAAE;AAClB,cAAI,MAAM,KAAM,IAAI,CAAE;AACtB,cAAI,MAAM,KAAM,IAAI,CAAE;AAEtB;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YAAG;AAAA,YAAG;AAAA,YACN;AAAA,UACD;AAEA;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YAAG;AAAA,YAAG;AAAA,YACN;AAAA,UACD;AAAA,QAED;AAAA,MAED;AAAA,IAED,OAAO;AAEN,cAAQ,KAAK,IAAK,GAAG,UAAU,KAAM;AACrC,YAAM,KAAK,IAAK,MAAM,OAAS,UAAU,QAAQ,UAAU,KAAQ;AAEnE,WAAM,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,GAAI;AAE3C,YAAI,MAAM,KAAM,CAAE;AAClB,YAAI,MAAM,KAAM,IAAI,CAAE;AACtB,YAAI,MAAM,KAAM,IAAI,CAAE;AAEtB;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UAAG;AAAA,UAAG;AAAA,UACN;AAAA,QACD;AAEA;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UAAG;AAAA,UAAG;AAAA,UACN;AAAA,QACD;AAAA,MAED;AAAA,IAED;AAAA,EAED,OAAO;AAIN,QAAK,MAAM,QAAS,QAAS,GAAI;AAEhC,WAAM,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,KAAO;AAE/C,gBAAQ,OAAQ,CAAE;AAElB,gBAAQ,KAAK,IAAK,MAAM,OAAO,UAAU,KAAM;AAC/C,cAAM,KAAK,IAAO,MAAM,QAAQ,MAAM,OAAW,UAAU,QAAQ,UAAU,KAAQ;AAErF,aAAM,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,GAAI;AAE3C,cAAI;AACJ,cAAI,IAAI;AACR,cAAI,IAAI;AAER;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YAAG;AAAA,YAAG;AAAA,YACN;AAAA,UACD;AAEA;AAAA,YACC;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YAAG;AAAA,YAAG;AAAA,YACN;AAAA,UACD;AAAA,QAED;AAAA,MAED;AAAA,IAED,OAAO;AAEN,cAAQ,KAAK,IAAK,GAAG,UAAU,KAAM;AACrC,YAAM,KAAK,IAAK,kBAAkB,OAAS,UAAU,QAAQ,UAAU,KAAQ;AAE/E,WAAM,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,GAAI;AAE3C,YAAI;AACJ,YAAI,IAAI;AACR,YAAI,IAAI;AAER;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UAAG;AAAA,UAAG;AAAA,UACN;AAAA,QACD;AAEA;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UAAG;AAAA,UAAG;AAAA,UACN;AAAA,QACD;AAAA,MAED;AAAA,IAED;AAAA,EAED;AAEA,QAAM,2BAA2B,IAAI,uBAAwB,kBAAkB,CAAE;AACjF,QAAM,yBAAyB,IAAI,uBAAwB,gBAAgB,CAAE;AAE7E,SAAO;AAAA,IAEN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAED;AAED;AAEA,SAAS,YAAa,UAAW;AAEhC,MAAK,SAAS,OAAO,WAAW,GAAI;AAEnC,YAAQ,KAAM,mFAAoF;AAClG,WAAO;AAAA,EAER;AAEA,MAAI,SAAS,SAAS;AAItB,WAAS,OAAO,KAAM,CAAE,GAAG,MAAO;AAEjC,QAAK,EAAE,kBAAkB,EAAE;AAAgB,aAAO,EAAE,gBAAgB,EAAE;AAEtE,WAAO,EAAE,QAAQ,EAAE;AAAA,EAEpB,CAAE;AAIF,MAAK,SAAS,SAAS,MAAM,MAAO;AAEnC,UAAM,oBAAoB,SAAS,aAAc,UAAW;AAC5D,UAAM,UAAU,CAAC;AAEjB,aAAU,IAAI,GAAG,IAAI,kBAAkB,OAAO,KAAK,GAAI;AAEtD,cAAQ,KAAM,GAAG,IAAI,GAAG,IAAI,CAAE;AAAA,IAE/B;AAEA,aAAS,SAAU,OAAQ;AAAA,EAE5B;AAIA,QAAM,QAAQ,SAAS,SAAS;AAEhC,QAAM,aAAa,CAAC;AAEpB,WAAU,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAO;AAE1C,UAAM,QAAQ,OAAQ,CAAE;AAExB,UAAM,aAAa,MAAM;AACzB,UAAM,cAAc,aAAa,MAAM;AAEvC,aAAU,IAAI,YAAY,IAAI,aAAa,KAAO;AAEjD,iBAAW,KAAM,MAAM,KAAM,CAAE,CAAE;AAAA,IAElC;AAAA,EAED;AAEA,WAAS,QAAQ;AACjB,WAAS,SAAU,UAAW;AAI9B,MAAI,QAAQ;AAEZ,WAAU,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAO;AAE1C,UAAM,QAAQ,OAAQ,CAAE;AAExB,UAAM,QAAQ;AACd,aAAS,MAAM;AAAA,EAEhB;AAIA,MAAI,eAAe,OAAQ,CAAE;AAE7B,WAAS,SAAS,CAAE,YAAa;AAEjC,WAAU,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAO;AAE1C,UAAM,QAAQ,OAAQ,CAAE;AAExB,QAAK,aAAa,kBAAkB,MAAM,eAAgB;AAEzD,mBAAa,SAAS,MAAM;AAAA,IAE7B,OAAO;AAEN,qBAAe;AACf,eAAS,OAAO,KAAM,YAAa;AAAA,IAEpC;AAAA,EAED;AAEA,SAAO;AAER;AAYA,SAAS,iBAAkB,UAAU,cAAc,KAAK,KAAK,GAAqB;AAEjF,QAAM,YAAY,KAAK,IAAK,WAAY;AACxC,QAAM,kBAAmB,IAAI,SAAU;AAGvC,QAAM,QAAQ,CAAE,IAAI,QAAQ,GAAG,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAE;AAC5D,QAAM,WAAW,IAAI,QAAQ;AAC7B,QAAM,WAAW,IAAI,QAAQ;AAC7B,QAAM,WAAW,IAAI,QAAQ;AAC7B,QAAM,YAAY,IAAI,QAAQ;AAG9B,WAAS,WAAY,GAAI;AAExB,UAAM,IAAI,CAAE,EAAI,EAAE,IAAI;AACtB,UAAM,IAAI,CAAE,EAAI,EAAE,IAAI;AACtB,UAAM,IAAI,CAAE,EAAI,EAAE,IAAI;AACtB,WAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,EAEtB;AAIA,QAAM,iBAAiB,SAAS,QAAQ,SAAS,aAAa,IAAI;AAClE,QAAM,UAAU,eAAe,WAAW;AAC1C,QAAM,YAAY,CAAC;AAGnB,WAAU,IAAI,GAAG,IAAI,QAAQ,QAAQ,GAAG,IAAI,GAAG,KAAO;AAErD,UAAM,KAAK,IAAI;AACf,UAAM,IAAI,MAAO,CAAE,EAAE,oBAAqB,SAAS,KAAK,CAAE;AAC1D,UAAM,IAAI,MAAO,CAAE,EAAE,oBAAqB,SAAS,KAAK,CAAE;AAC1D,UAAM,IAAI,MAAO,CAAE,EAAE,oBAAqB,SAAS,KAAK,CAAE;AAE1D,aAAS,WAAY,GAAG,CAAE;AAC1B,aAAS,WAAY,GAAG,CAAE;AAG1B,UAAM,SAAS,IAAI,QAAQ,EAAE,aAAc,UAAU,QAAS,EAAE,UAAU;AAC1E,aAAU,IAAI,GAAG,IAAI,GAAG,KAAO;AAE9B,YAAM,OAAO,MAAO,CAAE;AACtB,YAAM,OAAO,WAAY,IAAK;AAC9B,UAAK,EAAI,QAAQ,YAAc;AAE9B,kBAAW,IAAK,IAAI,CAAC;AAAA,MAEtB;AAEA,gBAAW,IAAK,EAAE,KAAM,MAAO;AAAA,IAEhC;AAAA,EAED;AAIA,QAAM,cAAc,IAAI,aAAc,QAAQ,QAAQ,CAAE;AACxD,QAAM,WAAW,IAAI,gBAAiB,aAAa,GAAG,KAAM;AAC5D,WAAU,IAAI,GAAG,IAAI,QAAQ,QAAQ,GAAG,IAAI,GAAG,KAAO;AAGrD,UAAM,KAAK,IAAI;AACf,UAAM,IAAI,MAAO,CAAE,EAAE,oBAAqB,SAAS,KAAK,CAAE;AAC1D,UAAM,IAAI,MAAO,CAAE,EAAE,oBAAqB,SAAS,KAAK,CAAE;AAC1D,UAAM,IAAI,MAAO,CAAE,EAAE,oBAAqB,SAAS,KAAK,CAAE;AAE1D,aAAS,WAAY,GAAG,CAAE;AAC1B,aAAS,WAAY,GAAG,CAAE;AAE1B,aAAS,aAAc,UAAU,QAAS,EAAE,UAAU;AAGtD,aAAU,IAAI,GAAG,IAAI,GAAG,KAAO;AAE9B,YAAM,OAAO,MAAO,CAAE;AACtB,YAAM,OAAO,WAAY,IAAK;AAC9B,YAAM,eAAe,UAAW,IAAK;AACrC,gBAAU,IAAK,GAAG,GAAG,CAAE;AAEvB,eAAU,IAAI,GAAG,KAAK,aAAa,QAAQ,IAAI,IAAI,KAAO;AAEzD,cAAM,YAAY,aAAc,CAAE;AAClC,YAAK,SAAS,IAAK,SAAU,IAAI,WAAY;AAE5C,oBAAU,IAAK,SAAU;AAAA,QAE1B;AAAA,MAED;AAEA,gBAAU,UAAU;AACpB,eAAS,OAAQ,KAAK,GAAG,UAAU,GAAG,UAAU,GAAG,UAAU,CAAE;AAAA,IAEhE;AAAA,EAED;AAEA,iBAAe,aAAc,UAAU,QAAS;AAChD,SAAO;AAER;", "names": ["object", "morphTargetsRelative", "a", "b", "c", "i", "il"]}