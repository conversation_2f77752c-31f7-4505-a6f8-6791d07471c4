{"version": 3, "sources": ["../../point-in-polygon/flat.js", "../../point-in-polygon/nested.js", "../../point-in-polygon/index.js"], "sourcesContent": ["module.exports = function pointInPolygonFlat (point, vs, start, end) {\n    var x = point[0], y = point[1];\n    var inside = false;\n    if (start === undefined) start = 0;\n    if (end === undefined) end = vs.length;\n    var len = (end-start)/2;\n    for (var i = 0, j = len - 1; i < len; j = i++) {\n        var xi = vs[start+i*2+0], yi = vs[start+i*2+1];\n        var xj = vs[start+j*2+0], yj = vs[start+j*2+1];\n        var intersect = ((yi > y) !== (yj > y))\n            && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n        if (intersect) inside = !inside;\n    }\n    return inside;\n};\n", "// ray-casting algorithm based on\n// https://wrf.ecse.rpi.edu/Research/Short_Notes/pnpoly.html\n\nmodule.exports = function pointInPolygonNested (point, vs, start, end) {\n    var x = point[0], y = point[1];\n    var inside = false;\n    if (start === undefined) start = 0;\n    if (end === undefined) end = vs.length;\n    var len = end - start;\n    for (var i = 0, j = len - 1; i < len; j = i++) {\n        var xi = vs[i+start][0], yi = vs[i+start][1];\n        var xj = vs[j+start][0], yj = vs[j+start][1];\n        var intersect = ((yi > y) !== (yj > y))\n            && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n        if (intersect) inside = !inside;\n    }\n    return inside;\n};\n", "var pointInPolygonFlat = require('./flat.js')\nvar pointInPolygonNested = require('./nested.js')\n\nmodule.exports = function pointInPolygon (point, vs, start, end) {\n    if (vs.length > 0 && Array.isArray(vs[0])) {\n        return pointInPolygonNested(point, vs, start, end);\n    } else {\n        return pointInPolygonFlat(point, vs, start, end);\n    }\n}\nmodule.exports.nested = pointInPolygonNested\nmodule.exports.flat = pointInPolygonFlat\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,WAAO,UAAU,SAAS,mBAAoB,OAAO,IAAI,OAAO,KAAK;AACjE,UAAI,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC;AAC7B,UAAI,SAAS;AACb,UAAI,UAAU;AAAW,gBAAQ;AACjC,UAAI,QAAQ;AAAW,cAAM,GAAG;AAChC,UAAI,OAAO,MAAI,SAAO;AACtB,eAAS,IAAI,GAAG,IAAI,MAAM,GAAG,IAAI,KAAK,IAAI,KAAK;AAC3C,YAAI,KAAK,GAAG,QAAM,IAAE,IAAE,CAAC,GAAG,KAAK,GAAG,QAAM,IAAE,IAAE,CAAC;AAC7C,YAAI,KAAK,GAAG,QAAM,IAAE,IAAE,CAAC,GAAG,KAAK,GAAG,QAAM,IAAE,IAAE,CAAC;AAC7C,YAAI,YAAc,KAAK,MAAQ,KAAK,KAC5B,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM;AAC/C,YAAI;AAAW,mBAAS,CAAC;AAAA,MAC7B;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACdA;AAAA;AAGA,WAAO,UAAU,SAAS,qBAAsB,OAAO,IAAI,OAAO,KAAK;AACnE,UAAI,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC;AAC7B,UAAI,SAAS;AACb,UAAI,UAAU;AAAW,gBAAQ;AACjC,UAAI,QAAQ;AAAW,cAAM,GAAG;AAChC,UAAI,MAAM,MAAM;AAChB,eAAS,IAAI,GAAG,IAAI,MAAM,GAAG,IAAI,KAAK,IAAI,KAAK;AAC3C,YAAI,KAAK,GAAG,IAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,IAAE,KAAK,EAAE,CAAC;AAC3C,YAAI,KAAK,GAAG,IAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,IAAE,KAAK,EAAE,CAAC;AAC3C,YAAI,YAAc,KAAK,MAAQ,KAAK,KAC5B,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM;AAC/C,YAAI;AAAW,mBAAS,CAAC;AAAA,MAC7B;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACjBA;AAAA;AAAA,QAAI,qBAAqB;AACzB,QAAI,uBAAuB;AAE3B,WAAO,UAAU,SAAS,eAAgB,OAAO,IAAI,OAAO,KAAK;AAC7D,UAAI,GAAG,SAAS,KAAK,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG;AACvC,eAAO,qBAAqB,OAAO,IAAI,OAAO,GAAG;AAAA,MACrD,OAAO;AACH,eAAO,mBAAmB,OAAO,IAAI,OAAO,GAAG;AAAA,MACnD;AAAA,IACJ;AACA,WAAO,QAAQ,SAAS;AACxB,WAAO,QAAQ,OAAO;AAAA;AAAA;", "names": []}