{"version": 3, "sources": ["../../autofit.js/dist/autofit.esm.js"], "sourcesContent": ["\n/**\n * @name: autofit.js\n * @author: <PERSON>\n * @version: 3.2.8\n * @description: autofit.js 是迄今为止最易用的自适应工具\n * @license: MIT\n */\n//#region src/index.ts\nlet currRenderDom = null;\nlet currelRectification = \"\";\nlet currelRectificationLevel = \"\";\nlet currelRectificationIsKeepRatio = \"\";\nlet resizeListener = null;\nlet timer = null;\nlet currScale = 1;\nlet isElRectification = false;\nconst autofit = {\n\tisAutofitRunning: false,\n\tinit(options = {}, isShowInitTip = true) {\n\t\tif (isShowInitTip) console.log(`autofit.js is running`);\n\t\tconst { dw = 1920, dh = 1080, el = typeof options === \"string\" ? options : \"body\", resize = true, ignore = [], transition = \"none\", delay = 0, limit = .1, cssMode = \"scale\", allowScroll = false } = options;\n\t\tcurrRenderDom = el;\n\t\tconst dom = document.querySelector(el);\n\t\tif (!dom) {\n\t\t\tconsole.error(`autofit: '${el}' is not exist`);\n\t\t\treturn;\n\t\t}\n\t\tconst style = document.createElement(\"style\");\n\t\tconst ignoreStyle = document.createElement(\"style\");\n\t\tstyle.lang = \"text/css\";\n\t\tignoreStyle.lang = \"text/css\";\n\t\tstyle.id = \"autofit-style\";\n\t\tignoreStyle.id = \"ignoreStyle\";\n\t\t!allowScroll && (style.innerHTML = `body {overflow: hidden;}`);\n\t\tconst bodyEl = document.querySelector(\"body\");\n\t\tbodyEl.appendChild(style);\n\t\tbodyEl.appendChild(ignoreStyle);\n\t\tdom.style.height = `${dh}px`;\n\t\tdom.style.width = `${dw}px`;\n\t\tdom.style.transformOrigin = `0 0`;\n\t\t!allowScroll && (dom.style.overflow = \"hidden\");\n\t\tkeepFit(dw, dh, dom, ignore, limit, cssMode);\n\t\tresizeListener = () => {\n\t\t\tclearTimeout(timer);\n\t\t\tif (delay != 0) timer = setTimeout(() => {\n\t\t\t\tkeepFit(dw, dh, dom, ignore, limit, cssMode);\n\t\t\t\tisElRectification && elRectification(currelRectification, currelRectificationIsKeepRatio, currelRectificationLevel);\n\t\t\t}, delay);\nelse {\n\t\t\t\tkeepFit(dw, dh, dom, ignore, limit, cssMode);\n\t\t\t\tisElRectification && elRectification(currelRectification, currelRectificationIsKeepRatio, currelRectificationLevel);\n\t\t\t}\n\t\t};\n\t\tresize && window.addEventListener(\"resize\", resizeListener);\n\t\tthis.isAutofitRunning = true;\n\t\tsetTimeout(() => {\n\t\t\tdom.style.transition = `${transition}s`;\n\t\t});\n\t},\n\toff(el = \"body\") {\n\t\ttry {\n\t\t\twindow.removeEventListener(\"resize\", resizeListener);\n\t\t\tconst autofitStyle = document.querySelector(\"#autofit-style\");\n\t\t\tautofitStyle && autofitStyle.remove();\n\t\t\tconst ignoreStyleDOM = document.querySelector(\"#ignoreStyle\");\n\t\t\tignoreStyleDOM && ignoreStyleDOM.remove();\n\t\t\tconst temp = document.querySelector(currRenderDom ? currRenderDom : el);\n\t\t\ttemp && (temp.style.cssText = \"\");\n\t\t\tisElRectification && offelRectification();\n\t\t} catch (error) {\n\t\t\tconsole.error(`autofit: Failed to remove normally`, error);\n\t\t}\n\t\tthis.isAutofitRunning = false;\n\t\tconsole.log(`autofit.js is off`);\n\t},\n\telRectification: null,\n\tscale: currScale\n};\nfunction elRectification(el, isKeepRatio = true, level = 1) {\n\tif (!autofit.isAutofitRunning) {\n\t\tconsole.error(\"autofit.js：(elRectification): autofit has not been initialized yet\");\n\t\treturn;\n\t}\n\toffelRectification();\n\t!el && console.error(`autofit.js：elRectification bad selector: ${el}`);\n\tcurrelRectification = el;\n\tcurrelRectificationLevel = level;\n\tcurrelRectificationIsKeepRatio = isKeepRatio;\n\tconst currEl = Array.from(document.querySelectorAll(el));\n\tif (currEl.length == 0) {\n\t\tconsole.error(`autofit.js：elRectification found no element by selector: \"${el}\"`);\n\t\treturn;\n\t}\n\tfor (const item of currEl) {\n\t\tconst rectification = currScale == 1 ? 1 : Number(currScale) * Number(level);\n\t\tif (!isElRectification) {\n\t\t\titem.originalWidth = item.clientWidth;\n\t\t\titem.originalHeight = item.clientHeight;\n\t\t}\n\t\tif (isKeepRatio) {\n\t\t\titem.style.width = `${item.originalWidth * rectification}px`;\n\t\t\titem.style.height = `${item.originalHeight * rectification}px`;\n\t\t} else {\n\t\t\titem.style.width = `${100 * rectification}%`;\n\t\t\titem.style.height = `${100 * rectification}%`;\n\t\t}\n\t\titem.style.transform = `translateZ(0) scale(${1 / Number(currScale)})`;\n\t\titem.style.transformOrigin = `0 0`;\n\t}\n\tisElRectification = true;\n}\nfunction offelRectification() {\n\tif (!currelRectification) return;\n\tisElRectification = false;\n\tfor (const item of Array.from(document.querySelectorAll(currelRectification))) {\n\t\titem.style.width = ``;\n\t\titem.style.height = ``;\n\t\titem.style.transform = ``;\n\t}\n}\nfunction keepFit(dw, dh, dom, ignore, limit, cssMode = \"scale\") {\n\tconst clientHeight = document.documentElement.clientHeight;\n\tconst clientWidth = document.documentElement.clientWidth;\n\tcurrScale = clientWidth / clientHeight < dw / dh ? clientWidth / dw : clientHeight / dh;\n\tcurrScale = Math.abs(1 - currScale) > limit ? currScale : 1;\n\tautofit.scale = +currScale;\n\tconst height = Math.round(clientHeight / Number(currScale));\n\tconst width = Math.round(clientWidth / Number(currScale));\n\tdom.style.height = `${height}px`;\n\tdom.style.width = `${width}px`;\n\tif (cssMode === \"zoom\") dom.style.zoom = `${currScale}`;\nelse dom.style.transform = `translateZ(0) scale(${currScale})`;\n\tconst ignoreStyleDOM = document.querySelector(\"#ignoreStyle\");\n\tignoreStyleDOM.innerHTML = \"\";\n\tfor (const temp of ignore) {\n\t\tconst item = temp;\n\t\tlet itemEl = item.el || item.dom;\n\t\ttypeof item == \"string\" && (itemEl = item);\n\t\tif (!itemEl || typeof itemEl === \"object\" && !Object.keys(itemEl).length) {\n\t\t\tconsole.error(`autofit: found invalid or empty selector/object: ${itemEl}`);\n\t\t\tcontinue;\n\t\t}\n\t\tconst realScale = item.scale ? item.scale : 1 / Number(currScale);\n\t\tconst realFontSize = realScale != currScale && item.fontSize;\n\t\tconst realWidth = realScale != currScale && item.width;\n\t\tconst realHeight = realScale != currScale && item.height;\n\t\tignoreStyleDOM.innerHTML += `\\n${itemEl} { \n      transform: scale(${realScale})!important;\n      transform-origin: 0 0;\n      ${realWidth ? `width: ${realWidth}!important;` : \"\"}\n      ${realHeight ? `height: ${realHeight}!important;` : \"\"}\n    }`;\n\t\tif (realFontSize) ignoreStyleDOM.innerHTML += `\\n${itemEl} div ,${itemEl} span,${itemEl} a,${itemEl} * {\n        font-size: ${realFontSize}px;\n      }`;\n\t}\n}\nautofit.elRectification = elRectification;\n\n//#endregion\nexport { autofit as default, elRectification };"], "mappings": ";;;AASA,IAAI,gBAAgB;AACpB,IAAI,sBAAsB;AAC1B,IAAI,2BAA2B;AAC/B,IAAI,iCAAiC;AACrC,IAAI,iBAAiB;AACrB,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI,oBAAoB;AACxB,IAAM,UAAU;AAAA,EACf,kBAAkB;AAAA,EAClB,KAAK,UAAU,CAAC,GAAG,gBAAgB,MAAM;AACxC,QAAI;AAAe,cAAQ,IAAI,uBAAuB;AACtD,UAAM,EAAE,KAAK,MAAM,KAAK,MAAM,KAAK,OAAO,YAAY,WAAW,UAAU,QAAQ,SAAS,MAAM,SAAS,CAAC,GAAG,aAAa,QAAQ,QAAQ,GAAG,QAAQ,KAAI,UAAU,SAAS,cAAc,MAAM,IAAI;AACtM,oBAAgB;AAChB,UAAM,MAAM,SAAS,cAAc,EAAE;AACrC,QAAI,CAAC,KAAK;AACT,cAAQ,MAAM,aAAa,EAAE,gBAAgB;AAC7C;AAAA,IACD;AACA,UAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,UAAM,cAAc,SAAS,cAAc,OAAO;AAClD,UAAM,OAAO;AACb,gBAAY,OAAO;AACnB,UAAM,KAAK;AACX,gBAAY,KAAK;AACjB,KAAC,gBAAgB,MAAM,YAAY;AACnC,UAAM,SAAS,SAAS,cAAc,MAAM;AAC5C,WAAO,YAAY,KAAK;AACxB,WAAO,YAAY,WAAW;AAC9B,QAAI,MAAM,SAAS,GAAG,EAAE;AACxB,QAAI,MAAM,QAAQ,GAAG,EAAE;AACvB,QAAI,MAAM,kBAAkB;AAC5B,KAAC,gBAAgB,IAAI,MAAM,WAAW;AACtC,YAAQ,IAAI,IAAI,KAAK,QAAQ,OAAO,OAAO;AAC3C,qBAAiB,MAAM;AACtB,mBAAa,KAAK;AAClB,UAAI,SAAS;AAAG,gBAAQ,WAAW,MAAM;AACxC,kBAAQ,IAAI,IAAI,KAAK,QAAQ,OAAO,OAAO;AAC3C,+BAAqB,gBAAgB,qBAAqB,gCAAgC,wBAAwB;AAAA,QACnH,GAAG,KAAK;AAAA,WACN;AACD,gBAAQ,IAAI,IAAI,KAAK,QAAQ,OAAO,OAAO;AAC3C,6BAAqB,gBAAgB,qBAAqB,gCAAgC,wBAAwB;AAAA,MACnH;AAAA,IACD;AACA,cAAU,OAAO,iBAAiB,UAAU,cAAc;AAC1D,SAAK,mBAAmB;AACxB,eAAW,MAAM;AAChB,UAAI,MAAM,aAAa,GAAG,UAAU;AAAA,IACrC,CAAC;AAAA,EACF;AAAA,EACA,IAAI,KAAK,QAAQ;AAChB,QAAI;AACH,aAAO,oBAAoB,UAAU,cAAc;AACnD,YAAM,eAAe,SAAS,cAAc,gBAAgB;AAC5D,sBAAgB,aAAa,OAAO;AACpC,YAAM,iBAAiB,SAAS,cAAc,cAAc;AAC5D,wBAAkB,eAAe,OAAO;AACxC,YAAM,OAAO,SAAS,cAAc,gBAAgB,gBAAgB,EAAE;AACtE,eAAS,KAAK,MAAM,UAAU;AAC9B,2BAAqB,mBAAmB;AAAA,IACzC,SAAS,OAAO;AACf,cAAQ,MAAM,sCAAsC,KAAK;AAAA,IAC1D;AACA,SAAK,mBAAmB;AACxB,YAAQ,IAAI,mBAAmB;AAAA,EAChC;AAAA,EACA,iBAAiB;AAAA,EACjB,OAAO;AACR;AACA,SAAS,gBAAgB,IAAI,cAAc,MAAM,QAAQ,GAAG;AAC3D,MAAI,CAAC,QAAQ,kBAAkB;AAC9B,YAAQ,MAAM,oEAAoE;AAClF;AAAA,EACD;AACA,qBAAmB;AACnB,GAAC,MAAM,QAAQ,MAAM,4CAA4C,EAAE,EAAE;AACrE,wBAAsB;AACtB,6BAA2B;AAC3B,mCAAiC;AACjC,QAAM,SAAS,MAAM,KAAK,SAAS,iBAAiB,EAAE,CAAC;AACvD,MAAI,OAAO,UAAU,GAAG;AACvB,YAAQ,MAAM,6DAA6D,EAAE,GAAG;AAChF;AAAA,EACD;AACA,aAAW,QAAQ,QAAQ;AAC1B,UAAM,gBAAgB,aAAa,IAAI,IAAI,OAAO,SAAS,IAAI,OAAO,KAAK;AAC3E,QAAI,CAAC,mBAAmB;AACvB,WAAK,gBAAgB,KAAK;AAC1B,WAAK,iBAAiB,KAAK;AAAA,IAC5B;AACA,QAAI,aAAa;AAChB,WAAK,MAAM,QAAQ,GAAG,KAAK,gBAAgB,aAAa;AACxD,WAAK,MAAM,SAAS,GAAG,KAAK,iBAAiB,aAAa;AAAA,IAC3D,OAAO;AACN,WAAK,MAAM,QAAQ,GAAG,MAAM,aAAa;AACzC,WAAK,MAAM,SAAS,GAAG,MAAM,aAAa;AAAA,IAC3C;AACA,SAAK,MAAM,YAAY,uBAAuB,IAAI,OAAO,SAAS,CAAC;AACnE,SAAK,MAAM,kBAAkB;AAAA,EAC9B;AACA,sBAAoB;AACrB;AACA,SAAS,qBAAqB;AAC7B,MAAI,CAAC;AAAqB;AAC1B,sBAAoB;AACpB,aAAW,QAAQ,MAAM,KAAK,SAAS,iBAAiB,mBAAmB,CAAC,GAAG;AAC9E,SAAK,MAAM,QAAQ;AACnB,SAAK,MAAM,SAAS;AACpB,SAAK,MAAM,YAAY;AAAA,EACxB;AACD;AACA,SAAS,QAAQ,IAAI,IAAI,KAAK,QAAQ,OAAO,UAAU,SAAS;AAC/D,QAAM,eAAe,SAAS,gBAAgB;AAC9C,QAAM,cAAc,SAAS,gBAAgB;AAC7C,cAAY,cAAc,eAAe,KAAK,KAAK,cAAc,KAAK,eAAe;AACrF,cAAY,KAAK,IAAI,IAAI,SAAS,IAAI,QAAQ,YAAY;AAC1D,UAAQ,QAAQ,CAAC;AACjB,QAAM,SAAS,KAAK,MAAM,eAAe,OAAO,SAAS,CAAC;AAC1D,QAAM,QAAQ,KAAK,MAAM,cAAc,OAAO,SAAS,CAAC;AACxD,MAAI,MAAM,SAAS,GAAG,MAAM;AAC5B,MAAI,MAAM,QAAQ,GAAG,KAAK;AAC1B,MAAI,YAAY;AAAQ,QAAI,MAAM,OAAO,GAAG,SAAS;AAAA;AACjD,QAAI,MAAM,YAAY,uBAAuB,SAAS;AAC1D,QAAM,iBAAiB,SAAS,cAAc,cAAc;AAC5D,iBAAe,YAAY;AAC3B,aAAW,QAAQ,QAAQ;AAC1B,UAAM,OAAO;AACb,QAAI,SAAS,KAAK,MAAM,KAAK;AAC7B,WAAO,QAAQ,aAAa,SAAS;AACrC,QAAI,CAAC,UAAU,OAAO,WAAW,YAAY,CAAC,OAAO,KAAK,MAAM,EAAE,QAAQ;AACzE,cAAQ,MAAM,oDAAoD,MAAM,EAAE;AAC1E;AAAA,IACD;AACA,UAAM,YAAY,KAAK,QAAQ,KAAK,QAAQ,IAAI,OAAO,SAAS;AAChE,UAAM,eAAe,aAAa,aAAa,KAAK;AACpD,UAAM,YAAY,aAAa,aAAa,KAAK;AACjD,UAAM,aAAa,aAAa,aAAa,KAAK;AAClD,mBAAe,aAAa;AAAA,EAAK,MAAM;AAAA,yBAChB,SAAS;AAAA;AAAA,QAE1B,YAAY,UAAU,SAAS,gBAAgB,EAAE;AAAA,QACjD,aAAa,WAAW,UAAU,gBAAgB,EAAE;AAAA;AAE1D,QAAI;AAAc,qBAAe,aAAa;AAAA,EAAK,MAAM,SAAS,MAAM,SAAS,MAAM,MAAM,MAAM;AAAA,qBAChF,YAAY;AAAA;AAAA,EAEhC;AACD;AACA,QAAQ,kBAAkB;", "names": []}