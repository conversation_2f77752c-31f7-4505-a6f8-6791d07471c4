{"version": 3, "sources": ["../../three/examples/jsm/lines/LineMaterial.js", "../../three/examples/jsm/lines/LineSegments2.js", "../../three/examples/jsm/lines/Line2.js"], "sourcesContent": ["/**\n * parameters = {\n *  color: <hex>,\n *  linewidth: <float>,\n *  dashed: <boolean>,\n *  dashScale: <float>,\n *  dashSize: <float>,\n *  dashOffset: <float>,\n *  gapSize: <float>,\n *  resolution: <Vector2>, // to be set by renderer\n * }\n */\n\nimport {\n\tShaderLib,\n\tShaderMaterial,\n\tUniformsLib,\n\tUniformsUtils,\n\tVector2\n} from 'three';\n\n\nUniformsLib.line = {\n\n\tworldUnits: { value: 1 },\n\tlinewidth: { value: 1 },\n\tresolution: { value: new Vector2( 1, 1 ) },\n\tdashOffset: { value: 0 },\n\tdashScale: { value: 1 },\n\tdashSize: { value: 1 },\n\tgapSize: { value: 1 } // todo FIX - maybe change to totalSize\n\n};\n\nShaderLib[ 'line' ] = {\n\n\tuniforms: UniformsUtils.merge( [\n\t\tUniformsLib.common,\n\t\tUniformsLib.fog,\n\t\tUniformsLib.line\n\t] ),\n\n\tvertexShader:\n\t/* glsl */`\n\t\t#include <common>\n\t\t#include <color_pars_vertex>\n\t\t#include <fog_pars_vertex>\n\t\t#include <logdepthbuf_pars_vertex>\n\t\t#include <clipping_planes_pars_vertex>\n\n\t\tuniform float linewidth;\n\t\tuniform vec2 resolution;\n\n\t\tattribute vec3 instanceStart;\n\t\tattribute vec3 instanceEnd;\n\n\t\tattribute vec3 instanceColorStart;\n\t\tattribute vec3 instanceColorEnd;\n\n\t\t#ifdef WORLD_UNITS\n\n\t\t\tvarying vec4 worldPos;\n\t\t\tvarying vec3 worldStart;\n\t\t\tvarying vec3 worldEnd;\n\n\t\t\t#ifdef USE_DASH\n\n\t\t\t\tvarying vec2 vUv;\n\n\t\t\t#endif\n\n\t\t#else\n\n\t\t\tvarying vec2 vUv;\n\n\t\t#endif\n\n\t\t#ifdef USE_DASH\n\n\t\t\tuniform float dashScale;\n\t\t\tattribute float instanceDistanceStart;\n\t\t\tattribute float instanceDistanceEnd;\n\t\t\tvarying float vLineDistance;\n\n\t\t#endif\n\n\t\tvoid trimSegment( const in vec4 start, inout vec4 end ) {\n\n\t\t\t// trim end segment so it terminates between the camera plane and the near plane\n\n\t\t\t// conservative estimate of the near plane\n\t\t\tfloat a = projectionMatrix[ 2 ][ 2 ]; // 3nd entry in 3th column\n\t\t\tfloat b = projectionMatrix[ 3 ][ 2 ]; // 3nd entry in 4th column\n\t\t\tfloat nearEstimate = - 0.5 * b / a;\n\n\t\t\tfloat alpha = ( nearEstimate - start.z ) / ( end.z - start.z );\n\n\t\t\tend.xyz = mix( start.xyz, end.xyz, alpha );\n\n\t\t}\n\n\t\tvoid main() {\n\n\t\t\t#ifdef USE_COLOR\n\n\t\t\t\tvColor.xyz = ( position.y < 0.5 ) ? instanceColorStart : instanceColorEnd;\n\n\t\t\t#endif\n\n\t\t\t#ifdef USE_DASH\n\n\t\t\t\tvLineDistance = ( position.y < 0.5 ) ? dashScale * instanceDistanceStart : dashScale * instanceDistanceEnd;\n\t\t\t\tvUv = uv;\n\n\t\t\t#endif\n\n\t\t\tfloat aspect = resolution.x / resolution.y;\n\n\t\t\t// camera space\n\t\t\tvec4 start = modelViewMatrix * vec4( instanceStart, 1.0 );\n\t\t\tvec4 end = modelViewMatrix * vec4( instanceEnd, 1.0 );\n\n\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\tworldStart = start.xyz;\n\t\t\t\tworldEnd = end.xyz;\n\n\t\t\t#else\n\n\t\t\t\tvUv = uv;\n\n\t\t\t#endif\n\n\t\t\t// special case for perspective projection, and segments that terminate either in, or behind, the camera plane\n\t\t\t// clearly the gpu firmware has a way of addressing this issue when projecting into ndc space\n\t\t\t// but we need to perform ndc-space calculations in the shader, so we must address this issue directly\n\t\t\t// perhaps there is a more elegant solution -- WestLangley\n\n\t\t\tbool perspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 ); // 4th entry in the 3rd column\n\n\t\t\tif ( perspective ) {\n\n\t\t\t\tif ( start.z < 0.0 && end.z >= 0.0 ) {\n\n\t\t\t\t\ttrimSegment( start, end );\n\n\t\t\t\t} else if ( end.z < 0.0 && start.z >= 0.0 ) {\n\n\t\t\t\t\ttrimSegment( end, start );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// clip space\n\t\t\tvec4 clipStart = projectionMatrix * start;\n\t\t\tvec4 clipEnd = projectionMatrix * end;\n\n\t\t\t// ndc space\n\t\t\tvec3 ndcStart = clipStart.xyz / clipStart.w;\n\t\t\tvec3 ndcEnd = clipEnd.xyz / clipEnd.w;\n\n\t\t\t// direction\n\t\t\tvec2 dir = ndcEnd.xy - ndcStart.xy;\n\n\t\t\t// account for clip-space aspect ratio\n\t\t\tdir.x *= aspect;\n\t\t\tdir = normalize( dir );\n\n\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\tvec3 worldDir = normalize( end.xyz - start.xyz );\n\t\t\t\tvec3 tmpFwd = normalize( mix( start.xyz, end.xyz, 0.5 ) );\n\t\t\t\tvec3 worldUp = normalize( cross( worldDir, tmpFwd ) );\n\t\t\t\tvec3 worldFwd = cross( worldDir, worldUp );\n\t\t\t\tworldPos = position.y < 0.5 ? start: end;\n\n\t\t\t\t// height offset\n\t\t\t\tfloat hw = linewidth * 0.5;\n\t\t\t\tworldPos.xyz += position.x < 0.0 ? hw * worldUp : - hw * worldUp;\n\n\t\t\t\t// don't extend the line if we're rendering dashes because we\n\t\t\t\t// won't be rendering the endcaps\n\t\t\t\t#ifndef USE_DASH\n\n\t\t\t\t\t// cap extension\n\t\t\t\t\tworldPos.xyz += position.y < 0.5 ? - hw * worldDir : hw * worldDir;\n\n\t\t\t\t\t// add width to the box\n\t\t\t\t\tworldPos.xyz += worldFwd * hw;\n\n\t\t\t\t\t// endcaps\n\t\t\t\t\tif ( position.y > 1.0 || position.y < 0.0 ) {\n\n\t\t\t\t\t\tworldPos.xyz -= worldFwd * 2.0 * hw;\n\n\t\t\t\t\t}\n\n\t\t\t\t#endif\n\n\t\t\t\t// project the worldpos\n\t\t\t\tvec4 clip = projectionMatrix * worldPos;\n\n\t\t\t\t// shift the depth of the projected points so the line\n\t\t\t\t// segments overlap neatly\n\t\t\t\tvec3 clipPose = ( position.y < 0.5 ) ? ndcStart : ndcEnd;\n\t\t\t\tclip.z = clipPose.z * clip.w;\n\n\t\t\t#else\n\n\t\t\t\tvec2 offset = vec2( dir.y, - dir.x );\n\t\t\t\t// undo aspect ratio adjustment\n\t\t\t\tdir.x /= aspect;\n\t\t\t\toffset.x /= aspect;\n\n\t\t\t\t// sign flip\n\t\t\t\tif ( position.x < 0.0 ) offset *= - 1.0;\n\n\t\t\t\t// endcaps\n\t\t\t\tif ( position.y < 0.0 ) {\n\n\t\t\t\t\toffset += - dir;\n\n\t\t\t\t} else if ( position.y > 1.0 ) {\n\n\t\t\t\t\toffset += dir;\n\n\t\t\t\t}\n\n\t\t\t\t// adjust for linewidth\n\t\t\t\toffset *= linewidth;\n\n\t\t\t\t// adjust for clip-space to screen-space conversion // maybe resolution should be based on viewport ...\n\t\t\t\toffset /= resolution.y;\n\n\t\t\t\t// select end\n\t\t\t\tvec4 clip = ( position.y < 0.5 ) ? clipStart : clipEnd;\n\n\t\t\t\t// back to clip space\n\t\t\t\toffset *= clip.w;\n\n\t\t\t\tclip.xy += offset;\n\n\t\t\t#endif\n\n\t\t\tgl_Position = clip;\n\n\t\t\tvec4 mvPosition = ( position.y < 0.5 ) ? start : end; // this is an approximation\n\n\t\t\t#include <logdepthbuf_vertex>\n\t\t\t#include <clipping_planes_vertex>\n\t\t\t#include <fog_vertex>\n\n\t\t}\n\t\t`,\n\n\tfragmentShader:\n\t/* glsl */`\n\t\tuniform vec3 diffuse;\n\t\tuniform float opacity;\n\t\tuniform float linewidth;\n\n\t\t#ifdef USE_DASH\n\n\t\t\tuniform float dashOffset;\n\t\t\tuniform float dashSize;\n\t\t\tuniform float gapSize;\n\n\t\t#endif\n\n\t\tvarying float vLineDistance;\n\n\t\t#ifdef WORLD_UNITS\n\n\t\t\tvarying vec4 worldPos;\n\t\t\tvarying vec3 worldStart;\n\t\t\tvarying vec3 worldEnd;\n\n\t\t\t#ifdef USE_DASH\n\n\t\t\t\tvarying vec2 vUv;\n\n\t\t\t#endif\n\n\t\t#else\n\n\t\t\tvarying vec2 vUv;\n\n\t\t#endif\n\n\t\t#include <common>\n\t\t#include <color_pars_fragment>\n\t\t#include <fog_pars_fragment>\n\t\t#include <logdepthbuf_pars_fragment>\n\t\t#include <clipping_planes_pars_fragment>\n\n\t\tvec2 closestLineToLine(vec3 p1, vec3 p2, vec3 p3, vec3 p4) {\n\n\t\t\tfloat mua;\n\t\t\tfloat mub;\n\n\t\t\tvec3 p13 = p1 - p3;\n\t\t\tvec3 p43 = p4 - p3;\n\n\t\t\tvec3 p21 = p2 - p1;\n\n\t\t\tfloat d1343 = dot( p13, p43 );\n\t\t\tfloat d4321 = dot( p43, p21 );\n\t\t\tfloat d1321 = dot( p13, p21 );\n\t\t\tfloat d4343 = dot( p43, p43 );\n\t\t\tfloat d2121 = dot( p21, p21 );\n\n\t\t\tfloat denom = d2121 * d4343 - d4321 * d4321;\n\n\t\t\tfloat numer = d1343 * d4321 - d1321 * d4343;\n\n\t\t\tmua = numer / denom;\n\t\t\tmua = clamp( mua, 0.0, 1.0 );\n\t\t\tmub = ( d1343 + d4321 * ( mua ) ) / d4343;\n\t\t\tmub = clamp( mub, 0.0, 1.0 );\n\n\t\t\treturn vec2( mua, mub );\n\n\t\t}\n\n\t\tvoid main() {\n\n\t\t\t#include <clipping_planes_fragment>\n\n\t\t\t#ifdef USE_DASH\n\n\t\t\t\tif ( vUv.y < - 1.0 || vUv.y > 1.0 ) discard; // discard endcaps\n\n\t\t\t\tif ( mod( vLineDistance + dashOffset, dashSize + gapSize ) > dashSize ) discard; // todo - FIX\n\n\t\t\t#endif\n\n\t\t\tfloat alpha = opacity;\n\n\t\t\t#ifdef WORLD_UNITS\n\n\t\t\t\t// Find the closest points on the view ray and the line segment\n\t\t\t\tvec3 rayEnd = normalize( worldPos.xyz ) * 1e5;\n\t\t\t\tvec3 lineDir = worldEnd - worldStart;\n\t\t\t\tvec2 params = closestLineToLine( worldStart, worldEnd, vec3( 0.0, 0.0, 0.0 ), rayEnd );\n\n\t\t\t\tvec3 p1 = worldStart + lineDir * params.x;\n\t\t\t\tvec3 p2 = rayEnd * params.y;\n\t\t\t\tvec3 delta = p1 - p2;\n\t\t\t\tfloat len = length( delta );\n\t\t\t\tfloat norm = len / linewidth;\n\n\t\t\t\t#ifndef USE_DASH\n\n\t\t\t\t\t#ifdef USE_ALPHA_TO_COVERAGE\n\n\t\t\t\t\t\tfloat dnorm = fwidth( norm );\n\t\t\t\t\t\talpha = 1.0 - smoothstep( 0.5 - dnorm, 0.5 + dnorm, norm );\n\n\t\t\t\t\t#else\n\n\t\t\t\t\t\tif ( norm > 0.5 ) {\n\n\t\t\t\t\t\t\tdiscard;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t#endif\n\n\t\t\t\t#endif\n\n\t\t\t#else\n\n\t\t\t\t#ifdef USE_ALPHA_TO_COVERAGE\n\n\t\t\t\t\t// artifacts appear on some hardware if a derivative is taken within a conditional\n\t\t\t\t\tfloat a = vUv.x;\n\t\t\t\t\tfloat b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;\n\t\t\t\t\tfloat len2 = a * a + b * b;\n\t\t\t\t\tfloat dlen = fwidth( len2 );\n\n\t\t\t\t\tif ( abs( vUv.y ) > 1.0 ) {\n\n\t\t\t\t\t\talpha = 1.0 - smoothstep( 1.0 - dlen, 1.0 + dlen, len2 );\n\n\t\t\t\t\t}\n\n\t\t\t\t#else\n\n\t\t\t\t\tif ( abs( vUv.y ) > 1.0 ) {\n\n\t\t\t\t\t\tfloat a = vUv.x;\n\t\t\t\t\t\tfloat b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;\n\t\t\t\t\t\tfloat len2 = a * a + b * b;\n\n\t\t\t\t\t\tif ( len2 > 1.0 ) discard;\n\n\t\t\t\t\t}\n\n\t\t\t\t#endif\n\n\t\t\t#endif\n\n\t\t\tvec4 diffuseColor = vec4( diffuse, alpha );\n\n\t\t\t#include <logdepthbuf_fragment>\n\t\t\t#include <color_fragment>\n\n\t\t\tgl_FragColor = vec4( diffuseColor.rgb, alpha );\n\n\t\t\t#include <tonemapping_fragment>\n\t\t\t#include <colorspace_fragment>\n\t\t\t#include <fog_fragment>\n\t\t\t#include <premultiplied_alpha_fragment>\n\n\t\t}\n\t\t`\n};\n\nclass LineMaterial extends ShaderMaterial {\n\n\tconstructor( parameters ) {\n\n\t\tsuper( {\n\n\t\t\ttype: 'LineMaterial',\n\n\t\t\tuniforms: UniformsUtils.clone( ShaderLib[ 'line' ].uniforms ),\n\n\t\t\tvertexShader: ShaderLib[ 'line' ].vertexShader,\n\t\t\tfragmentShader: ShaderLib[ 'line' ].fragmentShader,\n\n\t\t\tclipping: true // required for clipping support\n\n\t\t} );\n\n\t\tthis.isLineMaterial = true;\n\n\t\tthis.setValues( parameters );\n\n\t}\n\n\tget color() {\n\n\t\treturn this.uniforms.diffuse.value;\n\n\t}\n\n\tset color( value ) {\n\n\t\tthis.uniforms.diffuse.value = value;\n\n\t}\n\n\tget worldUnits() {\n\n\t\treturn 'WORLD_UNITS' in this.defines;\n\n\t}\n\n\tset worldUnits( value ) {\n\n\t\tif ( value === true ) {\n\n\t\t\tthis.defines.WORLD_UNITS = '';\n\n\t\t} else {\n\n\t\t\tdelete this.defines.WORLD_UNITS;\n\n\t\t}\n\n\t}\n\n\tget linewidth() {\n\n\t\treturn this.uniforms.linewidth.value;\n\n\t}\n\n\tset linewidth( value ) {\n\n\t\tif ( ! this.uniforms.linewidth ) return;\n\t\tthis.uniforms.linewidth.value = value;\n\n\t}\n\n\tget dashed() {\n\n\t\treturn 'USE_DASH' in this.defines;\n\n\t}\n\n\tset dashed( value ) {\n\n\t\tif ( ( value === true ) !== this.dashed ) {\n\n\t\t\tthis.needsUpdate = true;\n\n\t\t}\n\n\t\tif ( value === true ) {\n\n\t\t\tthis.defines.USE_DASH = '';\n\n\t\t} else {\n\n\t\t\tdelete this.defines.USE_DASH;\n\n\t\t}\n\n\t}\n\n\tget dashScale() {\n\n\t\treturn this.uniforms.dashScale.value;\n\n\t}\n\n\tset dashScale( value ) {\n\n\t\tthis.uniforms.dashScale.value = value;\n\n\t}\n\n\tget dashSize() {\n\n\t\treturn this.uniforms.dashSize.value;\n\n\t}\n\n\tset dashSize( value ) {\n\n\t\tthis.uniforms.dashSize.value = value;\n\n\t}\n\n\tget dashOffset() {\n\n\t\treturn this.uniforms.dashOffset.value;\n\n\t}\n\n\tset dashOffset( value ) {\n\n\t\tthis.uniforms.dashOffset.value = value;\n\n\t}\n\n\tget gapSize() {\n\n\t\treturn this.uniforms.gapSize.value;\n\n\t}\n\n\tset gapSize( value ) {\n\n\t\tthis.uniforms.gapSize.value = value;\n\n\t}\n\n\tget opacity() {\n\n\t\treturn this.uniforms.opacity.value;\n\n\t}\n\n\tset opacity( value ) {\n\n\t\tif ( ! this.uniforms ) return;\n\t\tthis.uniforms.opacity.value = value;\n\n\t}\n\n\tget resolution() {\n\n\t\treturn this.uniforms.resolution.value;\n\n\t}\n\n\tset resolution( value ) {\n\n\t\tthis.uniforms.resolution.value.copy( value );\n\n\t}\n\n\tget alphaToCoverage() {\n\n\t\treturn 'USE_ALPHA_TO_COVERAGE' in this.defines;\n\n\t}\n\n\tset alphaToCoverage( value ) {\n\n\t\tif ( ! this.defines ) return;\n\n\t\tif ( ( value === true ) !== this.alphaToCoverage ) {\n\n\t\t\tthis.needsUpdate = true;\n\n\t\t}\n\n\t\tif ( value === true ) {\n\n\t\t\tthis.defines.USE_ALPHA_TO_COVERAGE = '';\n\t\t\tthis.extensions.derivatives = true;\n\n\t\t} else {\n\n\t\t\tdelete this.defines.USE_ALPHA_TO_COVERAGE;\n\t\t\tthis.extensions.derivatives = false;\n\n\t\t}\n\n\t}\n\n}\n\nexport { LineMaterial };\n", "import {\n\tBox3,\n\tInstancedInterleaved<PERSON><PERSON><PERSON>,\n\tInterleavedBuffer<PERSON>tt<PERSON><PERSON>e,\n\tLine3,\n\tMathUtils,\n\tMatrix4,\n\tMesh,\n\tSphere,\n\tVector3,\n\tVector4\n} from 'three';\nimport { LineSegmentsGeometry } from '../lines/LineSegmentsGeometry.js';\nimport { LineMaterial } from '../lines/LineMaterial.js';\n\nconst _start = new Vector3();\nconst _end = new Vector3();\n\nconst _start4 = new Vector4();\nconst _end4 = new Vector4();\n\nconst _ssOrigin = new Vector4();\nconst _ssOrigin3 = new Vector3();\nconst _mvMatrix = new Matrix4();\nconst _line = new Line3();\nconst _closestPoint = new Vector3();\n\nconst _box = new Box3();\nconst _sphere = new Sphere();\nconst _clipToWorldVector = new Vector4();\n\nlet _ray, _lineWidth;\n\n// Returns the margin required to expand by in world space given the distance from the camera,\n// line width, resolution, and camera projection\nfunction getWorldSpaceHalfWidth( camera, distance, resolution ) {\n\n\t// transform into clip space, adjust the x and y values by the pixel width offset, then\n\t// transform back into world space to get world offset. Note clip space is [-1, 1] so full\n\t// width does not need to be halved.\n\t_clipToWorldVector.set( 0, 0, - distance, 1.0 ).applyMatrix4( camera.projectionMatrix );\n\t_clipToWorldVector.multiplyScalar( 1.0 / _clipToWorldVector.w );\n\t_clipToWorldVector.x = _lineWidth / resolution.width;\n\t_clipToWorldVector.y = _lineWidth / resolution.height;\n\t_clipToWorldVector.applyMatrix4( camera.projectionMatrixInverse );\n\t_clipToWorldVector.multiplyScalar( 1.0 / _clipToWorldVector.w );\n\n\treturn Math.abs( Math.max( _clipToWorldVector.x, _clipToWorldVector.y ) );\n\n}\n\nfunction raycastWorldUnits( lineSegments, intersects ) {\n\n\tconst matrixWorld = lineSegments.matrixWorld;\n\tconst geometry = lineSegments.geometry;\n\tconst instanceStart = geometry.attributes.instanceStart;\n\tconst instanceEnd = geometry.attributes.instanceEnd;\n\tconst segmentCount = Math.min( geometry.instanceCount, instanceStart.count );\n\n\tfor ( let i = 0, l = segmentCount; i < l; i ++ ) {\n\n\t\t_line.start.fromBufferAttribute( instanceStart, i );\n\t\t_line.end.fromBufferAttribute( instanceEnd, i );\n\n\t\t_line.applyMatrix4( matrixWorld );\n\n\t\tconst pointOnLine = new Vector3();\n\t\tconst point = new Vector3();\n\n\t\t_ray.distanceSqToSegment( _line.start, _line.end, point, pointOnLine );\n\t\tconst isInside = point.distanceTo( pointOnLine ) < _lineWidth * 0.5;\n\n\t\tif ( isInside ) {\n\n\t\t\tintersects.push( {\n\t\t\t\tpoint,\n\t\t\t\tpointOnLine,\n\t\t\t\tdistance: _ray.origin.distanceTo( point ),\n\t\t\t\tobject: lineSegments,\n\t\t\t\tface: null,\n\t\t\t\tfaceIndex: i,\n\t\t\t\tuv: null,\n\t\t\t\tuv1: null,\n\t\t\t} );\n\n\t\t}\n\n\t}\n\n}\n\nfunction raycastScreenSpace( lineSegments, camera, intersects ) {\n\n\tconst projectionMatrix = camera.projectionMatrix;\n\tconst material = lineSegments.material;\n\tconst resolution = material.resolution;\n\tconst matrixWorld = lineSegments.matrixWorld;\n\n\tconst geometry = lineSegments.geometry;\n\tconst instanceStart = geometry.attributes.instanceStart;\n\tconst instanceEnd = geometry.attributes.instanceEnd;\n\tconst segmentCount = Math.min( geometry.instanceCount, instanceStart.count );\n\n\tconst near = - camera.near;\n\n\t//\n\n\t// pick a point 1 unit out along the ray to avoid the ray origin\n\t// sitting at the camera origin which will cause \"w\" to be 0 when\n\t// applying the projection matrix.\n\t_ray.at( 1, _ssOrigin );\n\n\t// ndc space [ - 1.0, 1.0 ]\n\t_ssOrigin.w = 1;\n\t_ssOrigin.applyMatrix4( camera.matrixWorldInverse );\n\t_ssOrigin.applyMatrix4( projectionMatrix );\n\t_ssOrigin.multiplyScalar( 1 / _ssOrigin.w );\n\n\t// screen space\n\t_ssOrigin.x *= resolution.x / 2;\n\t_ssOrigin.y *= resolution.y / 2;\n\t_ssOrigin.z = 0;\n\n\t_ssOrigin3.copy( _ssOrigin );\n\n\t_mvMatrix.multiplyMatrices( camera.matrixWorldInverse, matrixWorld );\n\n\tfor ( let i = 0, l = segmentCount; i < l; i ++ ) {\n\n\t\t_start4.fromBufferAttribute( instanceStart, i );\n\t\t_end4.fromBufferAttribute( instanceEnd, i );\n\n\t\t_start4.w = 1;\n\t\t_end4.w = 1;\n\n\t\t// camera space\n\t\t_start4.applyMatrix4( _mvMatrix );\n\t\t_end4.applyMatrix4( _mvMatrix );\n\n\t\t// skip the segment if it's entirely behind the camera\n\t\tconst isBehindCameraNear = _start4.z > near && _end4.z > near;\n\t\tif ( isBehindCameraNear ) {\n\n\t\t\tcontinue;\n\n\t\t}\n\n\t\t// trim the segment if it extends behind camera near\n\t\tif ( _start4.z > near ) {\n\n\t\t\tconst deltaDist = _start4.z - _end4.z;\n\t\t\tconst t = ( _start4.z - near ) / deltaDist;\n\t\t\t_start4.lerp( _end4, t );\n\n\t\t} else if ( _end4.z > near ) {\n\n\t\t\tconst deltaDist = _end4.z - _start4.z;\n\t\t\tconst t = ( _end4.z - near ) / deltaDist;\n\t\t\t_end4.lerp( _start4, t );\n\n\t\t}\n\n\t\t// clip space\n\t\t_start4.applyMatrix4( projectionMatrix );\n\t\t_end4.applyMatrix4( projectionMatrix );\n\n\t\t// ndc space [ - 1.0, 1.0 ]\n\t\t_start4.multiplyScalar( 1 / _start4.w );\n\t\t_end4.multiplyScalar( 1 / _end4.w );\n\n\t\t// screen space\n\t\t_start4.x *= resolution.x / 2;\n\t\t_start4.y *= resolution.y / 2;\n\n\t\t_end4.x *= resolution.x / 2;\n\t\t_end4.y *= resolution.y / 2;\n\n\t\t// create 2d segment\n\t\t_line.start.copy( _start4 );\n\t\t_line.start.z = 0;\n\n\t\t_line.end.copy( _end4 );\n\t\t_line.end.z = 0;\n\n\t\t// get closest point on ray to segment\n\t\tconst param = _line.closestPointToPointParameter( _ssOrigin3, true );\n\t\t_line.at( param, _closestPoint );\n\n\t\t// check if the intersection point is within clip space\n\t\tconst zPos = MathUtils.lerp( _start4.z, _end4.z, param );\n\t\tconst isInClipSpace = zPos >= - 1 && zPos <= 1;\n\n\t\tconst isInside = _ssOrigin3.distanceTo( _closestPoint ) < _lineWidth * 0.5;\n\n\t\tif ( isInClipSpace && isInside ) {\n\n\t\t\t_line.start.fromBufferAttribute( instanceStart, i );\n\t\t\t_line.end.fromBufferAttribute( instanceEnd, i );\n\n\t\t\t_line.start.applyMatrix4( matrixWorld );\n\t\t\t_line.end.applyMatrix4( matrixWorld );\n\n\t\t\tconst pointOnLine = new Vector3();\n\t\t\tconst point = new Vector3();\n\n\t\t\t_ray.distanceSqToSegment( _line.start, _line.end, point, pointOnLine );\n\n\t\t\tintersects.push( {\n\t\t\t\tpoint: point,\n\t\t\t\tpointOnLine: pointOnLine,\n\t\t\t\tdistance: _ray.origin.distanceTo( point ),\n\t\t\t\tobject: lineSegments,\n\t\t\t\tface: null,\n\t\t\t\tfaceIndex: i,\n\t\t\t\tuv: null,\n\t\t\t\tuv1: null,\n\t\t\t} );\n\n\t\t}\n\n\t}\n\n}\n\nclass LineSegments2 extends Mesh {\n\n\tconstructor( geometry = new LineSegmentsGeometry(), material = new LineMaterial( { color: Math.random() * 0xffffff } ) ) {\n\n\t\tsuper( geometry, material );\n\n\t\tthis.isLineSegments2 = true;\n\n\t\tthis.type = 'LineSegments2';\n\n\t}\n\n\t// for backwards-compatibility, but could be a method of LineSegmentsGeometry...\n\n\tcomputeLineDistances() {\n\n\t\tconst geometry = this.geometry;\n\n\t\tconst instanceStart = geometry.attributes.instanceStart;\n\t\tconst instanceEnd = geometry.attributes.instanceEnd;\n\t\tconst lineDistances = new Float32Array( 2 * instanceStart.count );\n\n\t\tfor ( let i = 0, j = 0, l = instanceStart.count; i < l; i ++, j += 2 ) {\n\n\t\t\t_start.fromBufferAttribute( instanceStart, i );\n\t\t\t_end.fromBufferAttribute( instanceEnd, i );\n\n\t\t\tlineDistances[ j ] = ( j === 0 ) ? 0 : lineDistances[ j - 1 ];\n\t\t\tlineDistances[ j + 1 ] = lineDistances[ j ] + _start.distanceTo( _end );\n\n\t\t}\n\n\t\tconst instanceDistanceBuffer = new InstancedInterleavedBuffer( lineDistances, 2, 1 ); // d0, d1\n\n\t\tgeometry.setAttribute( 'instanceDistanceStart', new InterleavedBufferAttribute( instanceDistanceBuffer, 1, 0 ) ); // d0\n\t\tgeometry.setAttribute( 'instanceDistanceEnd', new InterleavedBufferAttribute( instanceDistanceBuffer, 1, 1 ) ); // d1\n\n\t\treturn this;\n\n\t}\n\n\traycast( raycaster, intersects ) {\n\n\t\tconst worldUnits = this.material.worldUnits;\n\t\tconst camera = raycaster.camera;\n\n\t\tif ( camera === null && ! worldUnits ) {\n\n\t\t\tconsole.error( 'LineSegments2: \"Raycaster.camera\" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.' );\n\n\t\t}\n\n\t\tconst threshold = ( raycaster.params.Line2 !== undefined ) ? raycaster.params.Line2.threshold || 0 : 0;\n\n\t\t_ray = raycaster.ray;\n\n\t\tconst matrixWorld = this.matrixWorld;\n\t\tconst geometry = this.geometry;\n\t\tconst material = this.material;\n\n\t\t_lineWidth = material.linewidth + threshold;\n\n\t\t// check if we intersect the sphere bounds\n\t\tif ( geometry.boundingSphere === null ) {\n\n\t\t\tgeometry.computeBoundingSphere();\n\n\t\t}\n\n\t\t_sphere.copy( geometry.boundingSphere ).applyMatrix4( matrixWorld );\n\n\t\t// increase the sphere bounds by the worst case line screen space width\n\t\tlet sphereMargin;\n\t\tif ( worldUnits ) {\n\n\t\t\tsphereMargin = _lineWidth * 0.5;\n\n\t\t} else {\n\n\t\t\tconst distanceToSphere = Math.max( camera.near, _sphere.distanceToPoint( _ray.origin ) );\n\t\t\tsphereMargin = getWorldSpaceHalfWidth( camera, distanceToSphere, material.resolution );\n\n\t\t}\n\n\t\t_sphere.radius += sphereMargin;\n\n\t\tif ( _ray.intersectsSphere( _sphere ) === false ) {\n\n\t\t\treturn;\n\n\t\t}\n\n\t\t// check if we intersect the box bounds\n\t\tif ( geometry.boundingBox === null ) {\n\n\t\t\tgeometry.computeBoundingBox();\n\n\t\t}\n\n\t\t_box.copy( geometry.boundingBox ).applyMatrix4( matrixWorld );\n\n\t\t// increase the box bounds by the worst case line width\n\t\tlet boxMargin;\n\t\tif ( worldUnits ) {\n\n\t\t\tboxMargin = _lineWidth * 0.5;\n\n\t\t} else {\n\n\t\t\tconst distanceToBox = Math.max( camera.near, _box.distanceToPoint( _ray.origin ) );\n\t\t\tboxMargin = getWorldSpaceHalfWidth( camera, distanceToBox, material.resolution );\n\n\t\t}\n\n\t\t_box.expandByScalar( boxMargin );\n\n\t\tif ( _ray.intersectsBox( _box ) === false ) {\n\n\t\t\treturn;\n\n\t\t}\n\n\t\tif ( worldUnits ) {\n\n\t\t\traycastWorldUnits( this, intersects );\n\n\t\t} else {\n\n\t\t\traycastScreenSpace( this, camera, intersects );\n\n\t\t}\n\n\t}\n\n}\n\nexport { LineSegments2 };\n", "import { LineSegments2 } from '../lines/LineSegments2.js';\nimport { LineGeometry } from '../lines/LineGeometry.js';\nimport { LineMaterial } from '../lines/LineMaterial.js';\n\nclass Line2 extends LineSegments2 {\n\n\tconstructor( geometry = new LineGeometry(), material = new LineMaterial( { color: Math.random() * 0xffffff } ) ) {\n\n\t\tsuper( geometry, material );\n\n\t\tthis.isLine2 = true;\n\n\t\tthis.type = 'Line2';\n\n\t}\n\n}\n\nexport { Line2 };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAsBA,YAAY,OAAO;AAAA,EAElB,YAAY,EAAE,OAAO,EAAE;AAAA,EACvB,WAAW,EAAE,OAAO,EAAE;AAAA,EACtB,YAAY,EAAE,OAAO,IAAI,QAAS,GAAG,CAAE,EAAE;AAAA,EACzC,YAAY,EAAE,OAAO,EAAE;AAAA,EACvB,WAAW,EAAE,OAAO,EAAE;AAAA,EACtB,UAAU,EAAE,OAAO,EAAE;AAAA,EACrB,SAAS,EAAE,OAAO,EAAE;AAAA;AAErB;AAEA,UAAW,MAAO,IAAI;AAAA,EAErB,UAAU,cAAc,MAAO;AAAA,IAC9B,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,EACb,CAAE;AAAA,EAEF;AAAA;AAAA,IACU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqNV;AAAA;AAAA,IACU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgKX;AAEA,IAAM,eAAN,cAA2B,eAAe;AAAA,EAEzC,YAAa,YAAa;AAEzB,UAAO;AAAA,MAEN,MAAM;AAAA,MAEN,UAAU,cAAc,MAAO,UAAW,MAAO,EAAE,QAAS;AAAA,MAE5D,cAAc,UAAW,MAAO,EAAE;AAAA,MAClC,gBAAgB,UAAW,MAAO,EAAE;AAAA,MAEpC,UAAU;AAAA;AAAA,IAEX,CAAE;AAEF,SAAK,iBAAiB;AAEtB,SAAK,UAAW,UAAW;AAAA,EAE5B;AAAA,EAEA,IAAI,QAAQ;AAEX,WAAO,KAAK,SAAS,QAAQ;AAAA,EAE9B;AAAA,EAEA,IAAI,MAAO,OAAQ;AAElB,SAAK,SAAS,QAAQ,QAAQ;AAAA,EAE/B;AAAA,EAEA,IAAI,aAAa;AAEhB,WAAO,iBAAiB,KAAK;AAAA,EAE9B;AAAA,EAEA,IAAI,WAAY,OAAQ;AAEvB,QAAK,UAAU,MAAO;AAErB,WAAK,QAAQ,cAAc;AAAA,IAE5B,OAAO;AAEN,aAAO,KAAK,QAAQ;AAAA,IAErB;AAAA,EAED;AAAA,EAEA,IAAI,YAAY;AAEf,WAAO,KAAK,SAAS,UAAU;AAAA,EAEhC;AAAA,EAEA,IAAI,UAAW,OAAQ;AAEtB,QAAK,CAAE,KAAK,SAAS;AAAY;AACjC,SAAK,SAAS,UAAU,QAAQ;AAAA,EAEjC;AAAA,EAEA,IAAI,SAAS;AAEZ,WAAO,cAAc,KAAK;AAAA,EAE3B;AAAA,EAEA,IAAI,OAAQ,OAAQ;AAEnB,QAAO,UAAU,SAAW,KAAK,QAAS;AAEzC,WAAK,cAAc;AAAA,IAEpB;AAEA,QAAK,UAAU,MAAO;AAErB,WAAK,QAAQ,WAAW;AAAA,IAEzB,OAAO;AAEN,aAAO,KAAK,QAAQ;AAAA,IAErB;AAAA,EAED;AAAA,EAEA,IAAI,YAAY;AAEf,WAAO,KAAK,SAAS,UAAU;AAAA,EAEhC;AAAA,EAEA,IAAI,UAAW,OAAQ;AAEtB,SAAK,SAAS,UAAU,QAAQ;AAAA,EAEjC;AAAA,EAEA,IAAI,WAAW;AAEd,WAAO,KAAK,SAAS,SAAS;AAAA,EAE/B;AAAA,EAEA,IAAI,SAAU,OAAQ;AAErB,SAAK,SAAS,SAAS,QAAQ;AAAA,EAEhC;AAAA,EAEA,IAAI,aAAa;AAEhB,WAAO,KAAK,SAAS,WAAW;AAAA,EAEjC;AAAA,EAEA,IAAI,WAAY,OAAQ;AAEvB,SAAK,SAAS,WAAW,QAAQ;AAAA,EAElC;AAAA,EAEA,IAAI,UAAU;AAEb,WAAO,KAAK,SAAS,QAAQ;AAAA,EAE9B;AAAA,EAEA,IAAI,QAAS,OAAQ;AAEpB,SAAK,SAAS,QAAQ,QAAQ;AAAA,EAE/B;AAAA,EAEA,IAAI,UAAU;AAEb,WAAO,KAAK,SAAS,QAAQ;AAAA,EAE9B;AAAA,EAEA,IAAI,QAAS,OAAQ;AAEpB,QAAK,CAAE,KAAK;AAAW;AACvB,SAAK,SAAS,QAAQ,QAAQ;AAAA,EAE/B;AAAA,EAEA,IAAI,aAAa;AAEhB,WAAO,KAAK,SAAS,WAAW;AAAA,EAEjC;AAAA,EAEA,IAAI,WAAY,OAAQ;AAEvB,SAAK,SAAS,WAAW,MAAM,KAAM,KAAM;AAAA,EAE5C;AAAA,EAEA,IAAI,kBAAkB;AAErB,WAAO,2BAA2B,KAAK;AAAA,EAExC;AAAA,EAEA,IAAI,gBAAiB,OAAQ;AAE5B,QAAK,CAAE,KAAK;AAAU;AAEtB,QAAO,UAAU,SAAW,KAAK,iBAAkB;AAElD,WAAK,cAAc;AAAA,IAEpB;AAEA,QAAK,UAAU,MAAO;AAErB,WAAK,QAAQ,wBAAwB;AACrC,WAAK,WAAW,cAAc;AAAA,IAE/B,OAAO;AAEN,aAAO,KAAK,QAAQ;AACpB,WAAK,WAAW,cAAc;AAAA,IAE/B;AAAA,EAED;AAED;;;ACzlBA,IAAM,SAAS,IAAI,QAAQ;AAC3B,IAAM,OAAO,IAAI,QAAQ;AAEzB,IAAM,UAAU,IAAI,QAAQ;AAC5B,IAAM,QAAQ,IAAI,QAAQ;AAE1B,IAAM,YAAY,IAAI,QAAQ;AAC9B,IAAM,aAAa,IAAI,QAAQ;AAC/B,IAAM,YAAY,IAAI,QAAQ;AAC9B,IAAM,QAAQ,IAAI,MAAM;AACxB,IAAM,gBAAgB,IAAI,QAAQ;AAElC,IAAM,OAAO,IAAI,KAAK;AACtB,IAAM,UAAU,IAAI,OAAO;AAC3B,IAAM,qBAAqB,IAAI,QAAQ;AAEvC,IAAI;AAAJ,IAAU;AAIV,SAAS,uBAAwB,QAAQ,UAAU,YAAa;AAK/D,qBAAmB,IAAK,GAAG,GAAG,CAAE,UAAU,CAAI,EAAE,aAAc,OAAO,gBAAiB;AACtF,qBAAmB,eAAgB,IAAM,mBAAmB,CAAE;AAC9D,qBAAmB,IAAI,aAAa,WAAW;AAC/C,qBAAmB,IAAI,aAAa,WAAW;AAC/C,qBAAmB,aAAc,OAAO,uBAAwB;AAChE,qBAAmB,eAAgB,IAAM,mBAAmB,CAAE;AAE9D,SAAO,KAAK,IAAK,KAAK,IAAK,mBAAmB,GAAG,mBAAmB,CAAE,CAAE;AAEzE;AAEA,SAAS,kBAAmB,cAAc,YAAa;AAEtD,QAAM,cAAc,aAAa;AACjC,QAAM,WAAW,aAAa;AAC9B,QAAM,gBAAgB,SAAS,WAAW;AAC1C,QAAM,cAAc,SAAS,WAAW;AACxC,QAAM,eAAe,KAAK,IAAK,SAAS,eAAe,cAAc,KAAM;AAE3E,WAAU,IAAI,GAAG,IAAI,cAAc,IAAI,GAAG,KAAO;AAEhD,UAAM,MAAM,oBAAqB,eAAe,CAAE;AAClD,UAAM,IAAI,oBAAqB,aAAa,CAAE;AAE9C,UAAM,aAAc,WAAY;AAEhC,UAAM,cAAc,IAAI,QAAQ;AAChC,UAAM,QAAQ,IAAI,QAAQ;AAE1B,SAAK,oBAAqB,MAAM,OAAO,MAAM,KAAK,OAAO,WAAY;AACrE,UAAM,WAAW,MAAM,WAAY,WAAY,IAAI,aAAa;AAEhE,QAAK,UAAW;AAEf,iBAAW,KAAM;AAAA,QAChB;AAAA,QACA;AAAA,QACA,UAAU,KAAK,OAAO,WAAY,KAAM;AAAA,QACxC,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,WAAW;AAAA,QACX,IAAI;AAAA,QACJ,KAAK;AAAA,MACN,CAAE;AAAA,IAEH;AAAA,EAED;AAED;AAEA,SAAS,mBAAoB,cAAc,QAAQ,YAAa;AAE/D,QAAM,mBAAmB,OAAO;AAChC,QAAM,WAAW,aAAa;AAC9B,QAAM,aAAa,SAAS;AAC5B,QAAM,cAAc,aAAa;AAEjC,QAAM,WAAW,aAAa;AAC9B,QAAM,gBAAgB,SAAS,WAAW;AAC1C,QAAM,cAAc,SAAS,WAAW;AACxC,QAAM,eAAe,KAAK,IAAK,SAAS,eAAe,cAAc,KAAM;AAE3E,QAAM,OAAO,CAAE,OAAO;AAOtB,OAAK,GAAI,GAAG,SAAU;AAGtB,YAAU,IAAI;AACd,YAAU,aAAc,OAAO,kBAAmB;AAClD,YAAU,aAAc,gBAAiB;AACzC,YAAU,eAAgB,IAAI,UAAU,CAAE;AAG1C,YAAU,KAAK,WAAW,IAAI;AAC9B,YAAU,KAAK,WAAW,IAAI;AAC9B,YAAU,IAAI;AAEd,aAAW,KAAM,SAAU;AAE3B,YAAU,iBAAkB,OAAO,oBAAoB,WAAY;AAEnE,WAAU,IAAI,GAAG,IAAI,cAAc,IAAI,GAAG,KAAO;AAEhD,YAAQ,oBAAqB,eAAe,CAAE;AAC9C,UAAM,oBAAqB,aAAa,CAAE;AAE1C,YAAQ,IAAI;AACZ,UAAM,IAAI;AAGV,YAAQ,aAAc,SAAU;AAChC,UAAM,aAAc,SAAU;AAG9B,UAAM,qBAAqB,QAAQ,IAAI,QAAQ,MAAM,IAAI;AACzD,QAAK,oBAAqB;AAEzB;AAAA,IAED;AAGA,QAAK,QAAQ,IAAI,MAAO;AAEvB,YAAM,YAAY,QAAQ,IAAI,MAAM;AACpC,YAAM,KAAM,QAAQ,IAAI,QAAS;AACjC,cAAQ,KAAM,OAAO,CAAE;AAAA,IAExB,WAAY,MAAM,IAAI,MAAO;AAE5B,YAAM,YAAY,MAAM,IAAI,QAAQ;AACpC,YAAM,KAAM,MAAM,IAAI,QAAS;AAC/B,YAAM,KAAM,SAAS,CAAE;AAAA,IAExB;AAGA,YAAQ,aAAc,gBAAiB;AACvC,UAAM,aAAc,gBAAiB;AAGrC,YAAQ,eAAgB,IAAI,QAAQ,CAAE;AACtC,UAAM,eAAgB,IAAI,MAAM,CAAE;AAGlC,YAAQ,KAAK,WAAW,IAAI;AAC5B,YAAQ,KAAK,WAAW,IAAI;AAE5B,UAAM,KAAK,WAAW,IAAI;AAC1B,UAAM,KAAK,WAAW,IAAI;AAG1B,UAAM,MAAM,KAAM,OAAQ;AAC1B,UAAM,MAAM,IAAI;AAEhB,UAAM,IAAI,KAAM,KAAM;AACtB,UAAM,IAAI,IAAI;AAGd,UAAM,QAAQ,MAAM,6BAA8B,YAAY,IAAK;AACnE,UAAM,GAAI,OAAO,aAAc;AAG/B,UAAM,OAAO,UAAU,KAAM,QAAQ,GAAG,MAAM,GAAG,KAAM;AACvD,UAAM,gBAAgB,QAAQ,MAAO,QAAQ;AAE7C,UAAM,WAAW,WAAW,WAAY,aAAc,IAAI,aAAa;AAEvE,QAAK,iBAAiB,UAAW;AAEhC,YAAM,MAAM,oBAAqB,eAAe,CAAE;AAClD,YAAM,IAAI,oBAAqB,aAAa,CAAE;AAE9C,YAAM,MAAM,aAAc,WAAY;AACtC,YAAM,IAAI,aAAc,WAAY;AAEpC,YAAM,cAAc,IAAI,QAAQ;AAChC,YAAM,QAAQ,IAAI,QAAQ;AAE1B,WAAK,oBAAqB,MAAM,OAAO,MAAM,KAAK,OAAO,WAAY;AAErE,iBAAW,KAAM;AAAA,QAChB;AAAA,QACA;AAAA,QACA,UAAU,KAAK,OAAO,WAAY,KAAM;AAAA,QACxC,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,WAAW;AAAA,QACX,IAAI;AAAA,QACJ,KAAK;AAAA,MACN,CAAE;AAAA,IAEH;AAAA,EAED;AAED;AAEA,IAAM,gBAAN,cAA4B,KAAK;AAAA,EAEhC,YAAa,WAAW,IAAI,qBAAqB,GAAG,WAAW,IAAI,aAAc,EAAE,OAAO,KAAK,OAAO,IAAI,SAAS,CAAE,GAAI;AAExH,UAAO,UAAU,QAAS;AAE1B,SAAK,kBAAkB;AAEvB,SAAK,OAAO;AAAA,EAEb;AAAA;AAAA,EAIA,uBAAuB;AAEtB,UAAM,WAAW,KAAK;AAEtB,UAAM,gBAAgB,SAAS,WAAW;AAC1C,UAAM,cAAc,SAAS,WAAW;AACxC,UAAM,gBAAgB,IAAI,aAAc,IAAI,cAAc,KAAM;AAEhE,aAAU,IAAI,GAAG,IAAI,GAAG,IAAI,cAAc,OAAO,IAAI,GAAG,KAAM,KAAK,GAAI;AAEtE,aAAO,oBAAqB,eAAe,CAAE;AAC7C,WAAK,oBAAqB,aAAa,CAAE;AAEzC,oBAAe,CAAE,IAAM,MAAM,IAAM,IAAI,cAAe,IAAI,CAAE;AAC5D,oBAAe,IAAI,CAAE,IAAI,cAAe,CAAE,IAAI,OAAO,WAAY,IAAK;AAAA,IAEvE;AAEA,UAAM,yBAAyB,IAAI,2BAA4B,eAAe,GAAG,CAAE;AAEnF,aAAS,aAAc,yBAAyB,IAAI,2BAA4B,wBAAwB,GAAG,CAAE,CAAE;AAC/G,aAAS,aAAc,uBAAuB,IAAI,2BAA4B,wBAAwB,GAAG,CAAE,CAAE;AAE7G,WAAO;AAAA,EAER;AAAA,EAEA,QAAS,WAAW,YAAa;AAEhC,UAAM,aAAa,KAAK,SAAS;AACjC,UAAM,SAAS,UAAU;AAEzB,QAAK,WAAW,QAAQ,CAAE,YAAa;AAEtC,cAAQ,MAAO,+HAAgI;AAAA,IAEhJ;AAEA,UAAM,YAAc,UAAU,OAAO,UAAU,SAAc,UAAU,OAAO,MAAM,aAAa,IAAI;AAErG,WAAO,UAAU;AAEjB,UAAM,cAAc,KAAK;AACzB,UAAM,WAAW,KAAK;AACtB,UAAM,WAAW,KAAK;AAEtB,iBAAa,SAAS,YAAY;AAGlC,QAAK,SAAS,mBAAmB,MAAO;AAEvC,eAAS,sBAAsB;AAAA,IAEhC;AAEA,YAAQ,KAAM,SAAS,cAAe,EAAE,aAAc,WAAY;AAGlE,QAAI;AACJ,QAAK,YAAa;AAEjB,qBAAe,aAAa;AAAA,IAE7B,OAAO;AAEN,YAAM,mBAAmB,KAAK,IAAK,OAAO,MAAM,QAAQ,gBAAiB,KAAK,MAAO,CAAE;AACvF,qBAAe,uBAAwB,QAAQ,kBAAkB,SAAS,UAAW;AAAA,IAEtF;AAEA,YAAQ,UAAU;AAElB,QAAK,KAAK,iBAAkB,OAAQ,MAAM,OAAQ;AAEjD;AAAA,IAED;AAGA,QAAK,SAAS,gBAAgB,MAAO;AAEpC,eAAS,mBAAmB;AAAA,IAE7B;AAEA,SAAK,KAAM,SAAS,WAAY,EAAE,aAAc,WAAY;AAG5D,QAAI;AACJ,QAAK,YAAa;AAEjB,kBAAY,aAAa;AAAA,IAE1B,OAAO;AAEN,YAAM,gBAAgB,KAAK,IAAK,OAAO,MAAM,KAAK,gBAAiB,KAAK,MAAO,CAAE;AACjF,kBAAY,uBAAwB,QAAQ,eAAe,SAAS,UAAW;AAAA,IAEhF;AAEA,SAAK,eAAgB,SAAU;AAE/B,QAAK,KAAK,cAAe,IAAK,MAAM,OAAQ;AAE3C;AAAA,IAED;AAEA,QAAK,YAAa;AAEjB,wBAAmB,MAAM,UAAW;AAAA,IAErC,OAAO;AAEN,yBAAoB,MAAM,QAAQ,UAAW;AAAA,IAE9C;AAAA,EAED;AAED;;;AClWA,IAAM,QAAN,cAAoB,cAAc;AAAA,EAEjC,YAAa,WAAW,IAAI,aAAa,GAAG,WAAW,IAAI,aAAc,EAAE,OAAO,KAAK,OAAO,IAAI,SAAS,CAAE,GAAI;AAEhH,UAAO,UAAU,QAAS;AAE1B,SAAK,UAAU;AAEf,SAAK,OAAO;AAAA,EAEb;AAED;", "names": []}